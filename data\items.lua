return {
    ['testburger'] = {
        label = 'Test Burger',
        weight = 220,
        degrade = 60,
        client = {
            image = 'burger_chicken.png',
            status = { hunger = 200000 },
            anim = 'eating',
            prop = 'burger',
            usetime = 2500,
            export = 'ox_inventory_examples.testburger'
        },
        server = {
            export = 'ox_inventory_examples.testburger',
            test = 'what an amazingly delicious burger, amirite?'
        },
        buttons = {
            {
                label = 'Lick it',
                action = function(slot)
                    print('You licked the burger')
                end
            },
            {
                label = 'Squeeze it',
                action = function(slot)
                    print('You squeezed the burger :(')
                end
            },
            {
                label = 'What do you call a vegan burger?',
                group = 'Hamburger Puns',
                action = function(slot)
                    print('A misteak.')
                end
            },
            {
                label = 'What do frogs like to eat with their hamburgers?',
                group = 'Hamburger Puns',
                action = function(slot)
                    print('French flies.')
                end
            },
            {
                label = 'Why were the burger and fries running?',
                group = 'Hamburger Puns',
                action = function(slot)
                    print('Because they\'re fast food.')
                end
            }
        },
        consume = 0.3
    },

    -- body cam & dash cam

    ['bodycam'] = {
        label = 'Body Camera',
        weight = 200,
        stack = false,
        close = false,
        consume = 0,
        description = 'A body camera for recording activities',
        server = {
            export = 'infc_bodycam.bodycam'
        },
        client = {
            image = 'bodycam.png',
        }
    },

    ['dashcam'] = {
        label = 'Dash Camera',
        weight = 300,
        stack = false,
        close = false,
        consume = 0,
        description = 'A dashboard camera for vehicles',
        server = {
            export = 'infc_bodycam.dashcam'
        },
        client = {
            image = 'dashcam.png',
        }
    },

    -- INFC Stress Items

    ['coffee'] = {
        label = 'Coffee',
        weight = 200,
        stack = true,
        close = true,
        description = 'Hot coffee to reduce stress',
        consume = 1,
        client = {
            status = { stress = -15 },
            anim = { dict = 'amb@world_human_drinking@coffee@male@idle_a', clip = 'idle_c', flag = 49 },
            prop = { model = 'p_amb_coffeecup_01', 
                    pos = vec3(0.0, 0.0, 0.0), 
                    rot = vec3(0.0, 0.0, 0.0),
                    bone = 28422 },
            usetime = 5000,
            notification = 'You drank the coffee and feel relaxed'
        }
    },
    
    ['cigarette_pack'] = {
        label = 'Cigarette Pack',
        weight = 100,
        stack = false,
        close = true,
        description = 'A pack containing cigarettes',
        consume = 0,
        client = {
            export = 'infc_stressrelief.useCigarettePack'
        }
    },
    
    ['cigarette'] = {
        label = 'Cigarette',
        weight = 10,
        stack = true,
        close = true,
        description = 'A cigarette to reduce stress (requires lighter)',
        consume = 1,
        client = {
            export = 'infc_stressrelief.useCigarette'
        }
    },
    
    ['lighter'] = {
        label = 'Lighter',
        weight = 50,
        stack = true,
        close = true,
        description = 'A lighter to light cigarettes',
        consume = 0
    },
    
    ['empty_cigarette_pack'] = {
        label = 'Empty Cigarette Pack',
        weight = 50,
        stack = true,
        close = true,
        description = 'An empty cigarette pack that can be disposed'
    },

    -- INFC Radar

        ['speed_ticket'] = {
        label = 'Speed Violation Ticket',
        weight = 1,
        stack = true,
        close = true,
        description = 'A traffic violation ticket for speeding. Contains fine details and payment information.',
        client = {
            image = 'speedticket.png',
        }
    },

    -- weapon parts

    ['pistol_parts'] = {
        label = 'Pistol Parts',
        weight = 100,
        stack = true,
        close = true,
        description = 'Parts used to repair pistols',
        client = {
            image = 'pistol_parts.png',
        }
    },

    ['smg_parts'] = {
        label = 'SMG Parts',
        weight = 100,
        stack = true,
        close = true,
        description = 'Parts used to repair submachine guns',
        client = {
            image = 'smg_parts.png',
        }
    },

    ['rifle_parts'] = {
        label = 'Rifle Parts',
        weight = 100,
        stack = true,
        close = true,
        description = 'Parts used to repair rifles',
        client = {
            image = 'rifle_parts.png',
        }
    },

    ['shotgun_parts'] = {
        label = 'Shotgun Parts',
        weight = 100,
        stack = true,
        close = true,
        description = 'Parts used to repair shotguns',
        client = {
            image = 'shotgun_parts.png',
        }
    },

    ['sniper_parts'] = {
        label = 'Sniper Parts',
        weight = 100,
        stack = true,
        close = true,
        description = 'Parts used to repair sniper rifles',
        client = {
            image = 'sniper_parts.png',
        }
    },
    
    -- INFC MDT

        ['mdt'] = {
        label = 'Mobile Data Terminal',
        weight = 800,
        stack = false,
        close = true,
        description = 'Police MDT Tablet for law enforcement officers',
        client = {
            export = 'qf-mdt-lspd.mdt'
        }
    },

    -- TIGER TICKET

    ['wisdom_ticket'] = {
    label = 'تذكرة الحكمة',
    weight = 10,
    stack = true,
    close = true,
    description = 'تذكرة تحتوي على جملة ملهمة عشوائية. استخدمها للحصول على حكمة جديدة!',
    client = {
        image = 'wisdom_ticket.png'
    },
    server = {
        export = 'infc_ticket.wisdom_ticket'
    }
},

    -- TIGER CV

['employment_record'] = {
    label = 'Employment Record',
    weight = 100,
    stack = false,
    close = true,
    description = 'Official employment record and CV document containing work history and statistics',
    client = {
        image = 'employment_record.png',
        }
    },

    -- TIGER_Vehicle
    ['vehicle_key'] = {
    label = 'Vehicle Key',
    weight = 50,
    stack = false,
    close = true,
    description = 'A physical key for a vehicle',
    client = {
        image = 'vehicle_key.png',
        }
    },
    -- outfit bag

    ['outfitbag_10'] = {
        label = 'Outfit Bag',
        weight = 0,
        stack = false,
        close = true,
        consume = 0,
        client = {
            export = 'infc_OutfitBag.placeBag'
        },
    },

    -- Hounting Items

    ['animal_tracker'] = {
    label = 'Animal Tracker',
    weight = 200,
    allowArmed = true,
    stack = false,
    },
    ['campfire'] = {
        label = 'Campfire',
        weight = 200,
        allowArmed = true,
        stack = false,
    },

    ['huntingbait'] = {
        label = 'Hunting Bait',
        weight = 100,
        allowArmed = true,
    },

    ['cooked_meat'] = {
        label = 'Cooked Meat',
        weight = 200,
    },
    ['raw_meat'] = {
        label = 'Raw Meat',
        weight = 200,
    },

    ['skin_deer_ruined'] = {
        label = 'Tattered Deer Pelt',
        weight = 200,
    },
    ['skin_deer_low'] = {
        label = 'Worn Deer Pelt',
        weight = 200,
    },
    ['skin_deer_medium'] = {
        label = 'Supple Deer Pelt',
        weight = 200,
    },
    ['skin_deer_good'] = {
        label = 'Prime Deer Pelt',
        weight = 200,
    },
    ['skin_deer_perfect'] = {
        label = 'Flawless Deer Pelt',
        weight = 200,
    },

    ['deer_horn'] = {
        label = 'Deer Horn',
        weight = 1000,
    },

    ['bank_card'] = {
        label = 'Bank Card',
        weight = 100,
        stack = false,
        close = true,
        description = 'A card used for ATM and banking services'
    },

    ['megaphone'] = {
        label = 'Megaphone',
        weight = 1,
        stack = true,
        close = true,
        description = nil
    },
    ['microphone'] = {
        label = 'Microphone',
        weight = 1,
        stack = true,
        close = true,
        description = nil
    },

    ['shield']  = {
		label = "Shield",
		weight = 100,
		stack = false,
		close = true,
		description = "Police Shield",
		client = {
			image = "shield.png",
		}
	},

    ['bdrone']  = {
		label = "Basic Drone 1",
		weight = 100,
		stack = false,
		close = true,
		description = "Enjoy Drone Exprience Basic Flyer 1",
		client = {
			image = "nethush_drone.png",
		}
	},

    ['badrone']  = {
		label = "Basic Drone 2",
		weight = 100,
		stack = false,
		close = true,
		description = "Enjoy Drone Exprience Basic Flyer 2",
		client = {
			image = "nethush_drone.png",
		}
	},

    ['bbdrone']  = {
		label = "Basic Drone 3",
		weight = 100,
		stack = false,
		close = true,
		description = "Enjoy Drone Exprience Basic Flyer 3",
		client = {
			image = "nethush_drone.png",
		}
	},

    ['bcdrone']  = {
		label = "Advanced Drone 1",
		weight = 100,
		stack = false,
		close = true,
		description = "Enjoy Drone Exprience Advanced Drone 1",
		client = {
			image = "nethush_drone.png",
		}
	},

    ['bddrone']  = {
		label = "Advanced Drone 2",
		weight = 100,
		stack = false,
		close = true,
		description = "Enjoy Drone Exprience Advanced Drone 2",
		client = {
			image = "nethush_drone.png",
		}
	},

    ['policedrone']  = {
		label = "Advanced Police Drone",
		weight = 100,
		stack = false,
		close = true,
		description = "Enjoy Drone Exprience Advanced Police Drone",
		client = {
			image = "nethush_drone.png",
		}
	},

    ['gangtable'] = {
        label = 'Gang Table',
        weight = 100,
        stack = false,
        close = true,
        description = 'Interactive gang table for gang management',
        server = {
            export = 'zat-gangs.useGangTable'
        }
    },

    ['gangcamera'] = {
        label = 'Gang Camera',
        weight = 50,
        stack = false,
        close = true,
        description = 'Security camera for gang territories',
        server = {
            export = 'zat-gangs.useGangCamera'
        }
    },

    ['gangstashs'] = {
        label = 'Small Gang Stash',
        weight = 200,
        stack = false,
        close = true,
        description = 'Small storage container for gang items',
        server = {
            export = 'zat-gangs.useGangStashS'
        }
    },

    ['gangstashm'] = {
        label = 'Medium Gang Stash',
        weight = 300,
        stack = false,
        close = true,
        description = 'Medium storage container for gang items',
        server = {
            export = 'zat-gangs.useGangStashM'
        }
    },

    ['spray'] = {
        label = 'Spray Paint',
        weight = 10,
        stack = true,
        close = true,
        description = 'Spray paint for gang graffiti',
        server = {
            export = 'zat-gangs.useSpray'
        }
    },

    ['sprayc'] = {
        label = 'Spray Cleaner',
        weight = 10,
        stack = true,
        close = true,
        description = 'Cleaner to remove gang graffiti',
        server = {
            export = 'zat-gangs.useSprayC'
        }
    },

    ['burger'] = {
        label = 'Burger',
        weight = 220,
        client = {
            status = { hunger = 200000 },
            anim = 'eating',
            prop = 'burger',
            usetime = 2500,
            notification = 'You ate a delicious burger'
        },
    },

    ['sprunk'] = {
        label = 'Sprunk',
        weight = 350,
        client = {
            status = { thirst = 200000 },
            anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
            prop = { model = `prop_ld_can_01`, pos = vec3(0.01, 0.01, 0.06), rot = vec3(5.0, 5.0, -180.5) },
            usetime = 2500,
            notification = 'You quenched your thirst with a sprunk'
        }
    },

    -- motel items

    ['infc_card'] = {
        label = 'Crastenburg Motel Card',
        weight = 10,
        stack = false,
        close = true,
        description = 'A key card for Crastenburg Motel room access. Shows room details when used.',
        client = {
            image = 'infc_card.png',
        }
    },

    ['parachute'] = {
        label = 'Parachute',
        weight = 8000,
        stack = false,
        client = {
            anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
            usetime = 1500
        }
    },

    ['garbage'] = {
        label = 'Garbage',
    },

    ['paperbag'] = {
        label = 'Paper Bag',
        weight = 1,
        stack = false,
        close = false,
        consume = 0
    },

    ['panties'] = {
        label = 'Knickers',
        weight = 10,
        consume = 1,
        client = {
            status = { thirst = -100000, stress = -25000 },
            anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
            prop = { model = `prop_cs_panties_02`, pos = vec3(0.03, 0.0, 0.02), rot = vec3(0.0, -13.5, -1.5) },
            usetime = 2500,
        }
    },

    ["phone"] = {
        label = "Phone",
        weight = 190,
        stack = false,
        consume = 0,
        client = {
            export = "lb-phone.UsePhoneItem",
            remove = function()
                TriggerEvent("lb-phone:itemRemoved")
            end,
            add = function()
                TriggerEvent("lb-phone:itemAdded")
            end
        }
    },

    ['mustard'] = {
        label = 'Mustard',
        weight = 500,
        client = {
            status = { hunger = 25000, thirst = 25000 },
            anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
            prop = { model = `prop_food_mustard`, pos = vec3(0.01, 0.0, -0.07), rot = vec3(1.0, 1.0, -1.5) },
            usetime = 2500,
            notification = 'You... drank mustard'
        }
    },

    ['water'] = {
        label = 'Water',
        weight = 500,
        client = {
            status = { thirst = 200000 },
            anim = { dict = 'mp_player_intdrink', clip = 'loop_bottle' },
            prop = { model = `prop_ld_flow_bottle`, pos = vec3(0.03, 0.03, 0.02), rot = vec3(0.0, 0.0, -1.5) },
            usetime = 2500,
            cancel = true,
            notification = 'You drank some refreshing water'
        }
    },

    ['armour'] = {
        label = 'Bulletproof Vest',
        weight = 3000,
        stack = false,
        client = {
            anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
            usetime = 3500
        }
    },
    -- ambulance items

    ['medicalbag'] = {
        label = 'Medical Bag',
        weight = 2000,
        stack = false,
        close = true,
        description = 'Contains all necessary medical equipment',
        client = {
            image = 'medicalbag.png',
        }
    },

    ['saline'] = {
        label = 'Saline',
        weight = 500,
        stack = true,
        close = true,
        description = 'Increase health & food over time and keep patient stable.',
        client = {
            image = 'saline.png',
        }
    },

    ['xray'] = {
        label = 'X-Ray Scanner',
        weight = 1000,
        stack = false,
        close = true,
        description = 'Used to x-ray scan patient to findout critical injuries.',
        client = {
            image = 'xray.png',
        }
    },

    ['lucas3'] = {
        label = 'Lucas 3',
        weight = 3000,
        stack = false,
        close = true,
        description = 'Chest Compression Device. Used when manual CPR fails.',
        client = {
            image = 'lucas3.png',
        }
    },

    ['neckbrace'] = {
        label = 'Neckbrace',
        weight = 200,
        stack = true,
        close = true,
        description = 'Pause head damage effect, heal head damage over time.',
        client = {
            image = 'neckbrace.png',
        }
    },

    ['bodybandage'] = {
        label = 'Body Bandage',
        weight = 150,
        stack = true,
        close = true,
        description = 'Pause chest damage effect, heal chest damage over time.',
        client = {
            image = 'bodybandage.png',
        }
    },

    ['armbrace'] = {
        label = 'Armbrace',
        weight = 150,
        stack = true,
        close = true,
        description = 'Pause arm damage effect, heal arm damage over time.',
        client = {
            image = 'armbrace.png',
        }
    },

    ['legbrace'] = {
        label = 'Legbrace',
        weight = 200,
        stack = true,
        close = true,
        description = 'Pause leg damage effect, heal leg damage over time.',
        client = {
            image = 'legbrace.png',
        }
    },

    -- Medicine Box Items
    ['medicinebox'] = {
        label = 'Medicine Box',
        weight = 1500,
        stack = false,
        close = true,
        description = 'Contains all necessary medicines',
        client = {
            image = 'medicinebox.png',
        }
    },

    ['bandage'] = {
        label = 'Bandage',
        weight = 50,
        stack = true,
        close = true,
        description = 'Pause damage effect & increase health 25%',
        client = {
            image = 'bandage.png',
        }
    },

    ['firstaid'] = {
        label = 'Firstaid',
        weight = 300,
        stack = true,
        close = true,
        description = 'Pause damage effect, heal all damages over time & increase health 85%',
        client = {
            image = 'firstaid.png',
        }
    },

    ['medikit'] = {
        label = 'Medikit',
        weight = 500,
        stack = true,
        close = true,
        description = 'Pause damage effect, heal all damages over time & increase health 100%',
        client = {
            image = 'medikit.png',
        }
    },

    ['morphine10'] = {
        label = 'Morphine 10mg',
        weight = 50,
        stack = true,
        close = true,
        description = '10mg tablet used to heal damages that less than 29%',
        client = {
            image = 'morphine10.png',
        }
    },

    ['paracetamol'] = {
        label = 'Paracetamol',
        weight = 30,
        stack = true,
        close = true,
        description = '10mg tablet pauses damage effect for 15 minutes',
        client = {
            image = 'paracetamol.png',
        }
    },

    ['morphine30'] = {
        label = 'Morphine 30mg',
        weight = 100,
        stack = true,
        close = true,
        description = '30mg injection pauses damage effect for 30 minutes',
        client = {
            image = 'morphine30.png',
        }
    },

    ['syringe'] = {
        label = 'Syringe',
        weight = 20,
        stack = true,
        close = true,
        description = 'Fresh syringe used to inject morphine 30mg',
        client = {
            image = 'syringe.png',
        }
    },

    ['stretcher'] = {
        label = 'Stretcher',
        weight = 5000,
        stack = false,
        close = true,
        description = 'Medical stretcher for transporting patients',
        client = {
            image = 'stretcher.png',
        }
    },

    ['wheelchair'] = {
        label = 'wheelchair',
        weight = 5000,
        stack = false,
        close = true,
        description = 'Medical wheelchair for transporting patients',
        client = {
            image = 'wheelchair.png',
        }
    },

    ['defibrillator'] = {
        label = 'Defibrillator',
        weight = 2000,
        stack = false,
        close = true,
        description = 'Emergency defibrillator for cardiac arrest',
        client = {
            image = 'defibrillator.png',
        }
    },

    -- Trainheist

    ['bag'] = {
            label = 'bag',
            weight = 50,
            stack = true,
            close = true
            },

    ['cutter'] = {
          label = 'Cutter',
          weight = 50,
          stack = true,
          close = true
        },

    -- Police Items

    ['broken_handcuffs'] = {
        label = 'Broken Handcuffs',
        weight = 100,
        stack = true,
        description = "It's broken, maybe you can repair it?"
    },
    ['cuffkeys'] = {
        label = 'Cuff Keys',
        weight = 75,
        stack = true,
        description = "Set them free !",
        consume = 0
    },
    ['ziptie'] = {
        label = 'ZipTie',
        weight = 50,
        stack = true,
        description = "Comes in handy when people misbehave. Maybe it can be used for something else? (Gang Members Only)",
        consume = 0,
        server = {
            export = 'zat-gangs.useZiptie'
        }
    },

    ['flush_cutter'] = {
        label = 'Flush Cutter',
        weight = 50,
        stack = true,
        description = "Comes in handy when you want to cut zipties.. (Gang Members Only)",
        consume = 0,
        server = {
            export = 'zat-gangs.useFlushCutter'
        }
    },
    ['bolt_cutter'] = {
        label = 'Bolt Cutter',
        weight = 50,
        stack = true,
        description = "Wanna cut some metal items ?",
        consume = 0
    },
    ['leo-gps'] = {
        label = 'LEO GPS',
        weight = 2000,
        stack = false,
        description = "Show your gps location to others",
        consume = 0
    },

    -- Trucker Items

    	['c4'] = {
		label = 'C4',
		weight = 10,
		stack = true,
		close = true,
        },

    -- House Rubbery

    	['hack_laptop'] = {
		label = 'Hacking Laptop',
		description = "",
		weight = 20,
		stack = true
	},

	['loot_bag'] = {
		label = 'Duffle bag',
		description = "",
		weight = 50,
		stack = true
	},

	['printer'] = {
		label = 'Printer',
		description = "",
		weight = 190,
		stack = true
	},

	['npc_phone'] = {
		label = 'Phone',
		description = "",
		weight = 10,
		stack = true
	},

	['monitor'] = {
		label = 'Monitor',
		description = "",
		weight = 50,
		stack = true
	},

	['television'] = {
		label = 'TV',
		description = "",
		weight = 155,
		stack = true
	},

	['flat_television'] = {
		label = 'Flat TV',
		description = "",
		weight = 155,
		stack = true
	},

	['radio_alarm'] = {
		label = 'Radio',
		description = "",
		weight = 30,
		stack = true
	},

	['fan'] = {
		label = 'Fan',
		description = "",
		weight = 20,
		stack = true
	},

	['lockpick'] = {
		label = 'Lockpick',
		description = "Can lockpick any doors if you have enough skill!",
		weight = 165,
		stack = true
	},

	['shoebox'] = {
		label = 'Shoe box',
		description = "",
		weight = 45,
		stack = true
	},

	['dj_deck'] = {
		label = 'DJ Deck',
		description = "",
		weight = 95,
		stack = true
	},

	['console'] = {
		label = 'Console',
		description = "",
		weight = 55,
		stack = true
	},

	['boombox'] = {
		label = 'Boombox',
		description = "",
		weight = 85,
		stack = true
	},

	['bong'] = {
		label = 'Bong',
		description = "",
		weight = 25,
		stack = true
	},

	['coffemachine'] = {
		label = 'Coffe machine',
		description = "",
		weight = 55,
		stack = true
	},

	['tapeplayer'] = {
		label = 'Tape Player',
		description = "",
		weight = 55,
		stack = true
	},

	['hairdryer'] = {
		label = 'Hairdryer',
		description = "",
		weight = 55,
		stack = true
	},

	['j_phone'] = {
		label = 'Phone',
		description = "",
		weight = 55,
		stack = true
	},

	['sculpture'] = {
		label = 'Sculpture',
		description = "",
		weight = 55,
		stack = true
	},

	['toiletry'] = {
		label = 'Toiletry',
		description = "",
		weight = 10,
		stack = true
	},

	['pogo'] = {
		label = 'Art Piece',
		description = "Pogo Statue",
		weight = 155,
		stack = true
	},

	['powder'] = {
		label = 'Bag with powder',
		description = "Good for discovering lasers that are not visible",
		weight = 50,
		stack = true
	},

	['bracelet'] = {
		label = 'Bracelet',
		description = "",
		weight = 25,
		stack = true
	},

	['book'] = {
		label = 'Book',
		description = "",
		weight = 25,
		stack = true
	},

	['earings'] = {
		label = 'Earings',
		description = "",
		weight = 25,
		stack = true
	},

	['gold_bracelet'] = {
		label = 'Gold bracelet',
		description = "",
		weight = 45,
		stack = true
	},

	['gold_watch'] = {
		label = 'Gold watch',
		weight = 55,
		stack = true
	},

	['house_locator'] = {
		label = 'House locator',
		weight = 55,
		stack = true
	},

	['necklace'] = {
		label = 'Necklace',
		weight = 55,
		stack = true
	},

	['notepad'] = {
		label = 'Notepad',
		weight = 5,
		stack = true
	},

	['pencil'] = {
		label = 'Pencil',
		weight = 25,
		stack = true
	},

	['romantic_book'] = {
		label = 'Romantic book',
		weight = 25,
		stack = true
	},

	['shampoo'] = {
		label = 'Shampoo',
		weight = 25,
		stack = true
	},

	['soap'] = {
		label = 'Soap',
		weight = 25,
		stack = true
	},

	['toothpaste'] = {
		label = 'Toothpaste',
		weight = 15,
		stack = true
	},

	['watch'] = {
		label = 'Watch',
		weight = 35,
		stack = true
	},
		
	['skull'] = {
		label = 'Skull Art with diamonds',
		weight = 95,
		stack = true
	},

    -- Rubbery Items

['apple_juice'] = {label = 'Apple Juice', description = '', weight = 5, stack = true},
['beer_box'] = {label = 'Beer Box', description = '', weight = 20, stack = true},
['cig_pack'] = {label = 'Cigarette Pack', description = '', weight = 5, stack = true},
['coke'] = {label = 'Coke', description = '', weight = 5, stack = true},
['ecola_big'] = {label = 'E-Cola Bottle', description = '', weight = 10, stack = true},
['ecola_small'] = {label = 'E-Cola Can', description = '', weight = 5, stack = true},
['fishsticks'] = {label = 'Fish Sticks', description = '', weight = 5, stack = true},
['flour'] = {label = 'Flour Bag', description = '', weight = 5, stack = true},
['gold'] = {label = 'Gold Bar', description = '', weight = 5, stack = true},
['champ'] = {label = 'Champagne', description = '', weight = 5, stack = true},
['chips'] = {label = 'Potato Chips', description = '', weight = 5, stack = true},
['jewels'] = {label = 'Jewels', description = '', weight = 5, stack = true},
['mayo'] = {label = 'Mayonnaise Jar', description = '', weight = 5, stack = true},
['orange_big'] = {label = 'Large Orange', description = '', weight = 5, stack = true},
['seafood'] = {label = 'Seafood Package', description = '', weight = 10, stack = true},
['sprunk_big'] = {label = 'Sprunk Bottle', description = '', weight = 10, stack = true},
['sweetcorn'] = {label = 'Sweet Corn', description = '', weight = 5, stack = true},
['weld_set'] = {label = 'Welding Set', description = '', weight = 15, stack = true},
['hack_set'] = {label = 'Hacking Set', description = '', weight = 15, stack = true},
['crowbar'] = {label = 'Crowbar', description = '', weight = 15, stack = true},
['fingerprint_tape'] = {label = 'Fingerprint collector', description = '', weight = 15, stack = true},
['plan'] = {label = 'Plan', description = '', weight = 15, stack = true},

    -- Jim-Mechanic --

    ['mechanic_tools'] = {
        label = "Mechanic tools", weight = 0, stack = false, close = true, description = "Needed for vehicle repairs",
        client = { image = "mechanic_tools.png", event = "jim-mechanic:client:Repair:Check" }
    },
    ['toolbox'] = {
        label = "Toolbox", weight = 0, stack = false, close = true, description = "Needed for Performance part removal",
        client = { image = "toolbox.png", event = "jim-mechanic:client:Menu" }
    },
    ['ducttape'] = {
        label = "Duct Tape", weight = 0, stack = false, close = true, description = "Good for quick fixes",
        client = { image = "bodyrepair.png", event = "jim-mechanic:quickrepair" }
    },
    ['mechboard'] = { label = 'Mechanic Sheet', weight = 0, stack = false, close = true,
        buttons = {
            { 	label = 'View List',
                action = function(slot)
                    local items = exports.ox_inventory:Search('slots', 'mechboard')
                    for _, v in pairs(items) do
                        if (v.slot == slot) then
                            local item = v
                            item.info = item.metadata['info'] or {}
                            TriggerEvent("jim-mechanic:client:giveList", item)
                            exports.ox_inventory:closeInventory()
                            break
                        end
                    end
                end
            },
            { 	label = 'Copy Parts List',
                action = function(slot)
                    local items = exports.ox_inventory:Search('slots', 'mechboard')
                    for _, v in pairs(items) do
                        if (v.slot == slot) then
                            lib.setClipboard(v.metadata.info.vehlist)
                            break
                        end
                    end
                end
            },
            { 	label = 'Copy Platedsdf Number',
                action = function(slot)
                    local items = exports.ox_inventory:Search('slots', 'mechboard')
                    for _, v in pairs(items) do
                        if (v.slot == slot) then
                            lib.setClipboard(v.metadata.info.vehplate)
                            break
                        end
                    end
                end
            },
            {	label = 'Copy Vehicle Model',
                action = function(slot)
                    local items = exports.ox_inventory:Search('slots', 'mechboard')
                    for _, v in pairs(items) do
                        if (v.slot == slot) then
                            lib.setClipboard(v.metadata.info.veh) break
                        end
                    end
                end
            },
        },
        client = {
            event = "jim-mechanic:client:giveList"
        }
    },
    --Performance
    ['turbo'] = {
        label = "Supercharger Turbo", weight = 0, stack = false, close = true, description = "Who doesn't need a 65mm Turbo??",
        client = { image = "turbo.png", event = "jim-mechanic:client:applyTurbo", remove = false },
    },
    ['car_armor'] = {
        label = "Vehicle Armor", weight = 0, stack = false, close = true, description = "",
        client = { image = "car_armour.png", event = "jim-mechanic:client:applyArmour", remove = false },
    },
    ['nos'] = {
        label = "NOS Bottle", weight = 0, stack = false, close = true, description = "A full bottle of NOS",
        client = { image = "nos.png", event = "jim-mechanic:client:applyNOS", },
    },
    ['noscan'] = {
        label = "Empty NOS Bottle", weight = 0, stack = true, close = true, description = "An Empty bottle of NOS",
        client = { image = "noscan.png", }
    },
    ['noscolour'] = {
        label = "NOS Colour Injector", weight = 0, stack = true, close = true, description = "Make that purge spray",
        client = { image = "noscolour.png", event = "jim-mechanic:client:NOS:rgbORhex", },
    },

    ['engine1'] = {
        label = "Tier 1 Engine", weight = 0, stack = false, close = true, description = "",
        client = { image = "engine1.png",  event = "jim-mechanic:client:applyEngine", level = 0, remove = false },
    },
    ['engine2'] = {
        label = "Tier 2 Engine", weight = 0, stack = false, close = true, description = "",
        client = { image = "engine2.png",  event = "jim-mechanic:client:applyEngine", level = 1, remove = false },
    },
    ['engine3'] = {
        label = "Tier 3 Engine", weight = 0, stack = false, close = true, description = "",
        client = { image = "engine3.png",  event = "jim-mechanic:client:applyEngine", level = 2, remove = false },
    },
    ['engine4'] = {
        label = "Tier 4 Engine", weight = 0, stack = false, close = true, description = "",
        client = { image = "engine4.png",  event = "jim-mechanic:client:applyEngine", level = 3, remove = false },
    },
    ['engine5'] = {
        label = "Tier 5 Engine", weight = 0, stack = false, close = true, description = "",
        client = { image = "engine5.png",  event = "jim-mechanic:client:applyEngine", level = 4, remove = false },
    },

    ['transmission1'] = {
        label = "Tier 1 Transmission", weight = 0, stack = false, close = true, description = "",
        client = { image = "transmission1.png",  event = "jim-mechanic:client:applyTransmission", level = 0, remove = false },
    },
    ['transmission2'] = {
        label = "Tier 2 Transmission", weight = 0, stack = false, close = true, description = "",
        client = { image = "transmission2.png",  event = "jim-mechanic:client:applyTransmission", level = 1, remove = false },
    },
    ['transmission3'] = {
        label = "Tier 3 Transmission", weight = 0, stack = false, close = true, description = "",
        client = { image = "transmission3.png",  event = "jim-mechanic:client:applyTransmission", level = 2, remove = false },
    },
    ['transmission4'] = {
        label = "Tier 4 Transmission", weight = 0, stack = false, close = true, description = "",
        client = { image = "transmission4.png",  event = "jim-mechanic:client:applyTransmission", level = 3, remove = false },
    },

    ['brakes1'] = {
        label = "Tier 1 Brakes", weight = 0, stack = false, close = true, description = "",
        client = { image = "brakes1.png",  event = "jim-mechanic:client:applyBrakes", level = 0, remove = false },
    },
    ['brakes2'] = {
        label = "Tier 2 Brakes", weight = 0, stack = false, close = true, description = "",
        client = { image = "brakes2.png",  event = "jim-mechanic:client:applyBrakes", level = 1, remove = false },
    },
    ['brakes3'] = {
        label = "Tier 3 Brakes", weight = 0, stack = false, close = true, description = "",
        client = { image = "brakes3.png",  event = "jim-mechanic:client:applyBrakes", level = 2, remove = false },
    },

    ['suspension1'] = {
        label = "Tier 1 Suspension", weight = 0, stack = false, close = true, description = "",
        client = { image = "suspension1.png", event = "jim-mechanic:client:applySuspension",  level = 0, remove = false },
    },
    ['suspension2'] = {
        label = "Tier 2 Suspension", weight = 0, stack = false, close = true, description = "",
        client = { image = "suspension2.png", event = "jim-mechanic:client:applySuspension", level = 1, remove = false },
    },
    ['suspension3'] = {
        label = "Tier 3 Suspension", weight = 0, stack = false, close = true, description = "",
        client = { image = "suspension3.png", event = "jim-mechanic:client:applySuspension", level = 2, remove = false },
    },
    ['suspension4'] = {
        label = "Tier 4 Suspension", weight = 0, stack = false, close = true, description = "",
        client = { image = "suspension4.png", event = "jim-mechanic:client:applySuspension", level = 3, remove = false },
    },
    ['suspension5'] = {
        label = "Tier 5 Suspension", weight = 0, stack = false, close = true, description = "",
        client = { image = "suspension5.png", event = "jim-mechanic:client:applySuspension", level = 4, remove = false },
    },

    ['bprooftires'] = {
        label = "Bulletproof Tires", weight = 0, stack = false, close = true, description = "",
        client = { image = "bprooftires.png", event = "jim-mechanic:client:applyBulletProof", remove = false },
    },
    ['drifttires'] = {
        label = "Drift Tires", weight = 0, stack = false, close = true, description = "",
        client = { image = "drifttires.png", event = "jim-mechanic:client:applyDrift", remove = false },
    },

    ['oilp1'] = {
        label = "Tier 1 Oil Pump", weight = 0, stack = false, close = true, description = "",
        client = { image = "oilp1.png", event = "jim-mechanic:client:applyExtraPart", level = 1, mod = "oilp", remove = false },
    },
    ['oilp2'] = {
        label = "Tier 2 Oil Pump", weight = 0, stack = false, close = true, description = "",
        client = { image = "oilp2.png", event = "jim-mechanic:client:applyExtraPart", level = 2, mod = "oilp", remove = false },
    },
    ['oilp3'] = {
        label = "Tier 3 Oil Pump", weight = 0, stack = false, close = true, description = "",
        client = { image = "oilp3.png", event = "jim-mechanic:client:applyExtraPart", level = 3, mod = "oilp", remove = false },
    },

    ['drives1'] = {
        label = "Tier 1 Drive Shaft", weight = 0, stack = false, close = true, description = "",
        client = { image = "drives1.png", event = "jim-mechanic:client:applyExtraPart", level = 1, mod = "drives", remove = false },
    },
    ['drives2'] = {
        label = "Tier 2 Drive Shaft", weight = 0, stack = false, close = true, description = "",
        client = { image = "drives2.png", event = "jim-mechanic:client:applyExtraPart", level = 2, mod = "drives", remove = false },
    },
    ['drives3'] = {
        label = "Tier 3 Drive Shaft", weight = 0, stack = false, close = true, description = "",
        client = { image = "drives3.png", event = "jim-mechanic:client:applyExtraPart", level = 3, mod = "drives", remove = false },
    },

    ['cylind1'] = {
        label = "Tier 1 Cylinder Head", weight = 0, stack = false, close = true, description = "",
        client = { image = "cylind1.png", event = "jim-mechanic:client:applyExtraPart", level = 1, mod = "cylind", remove = false },
    },
    ['cylind2'] = {
        label = "Tier 2 Cylinder Head", weight = 0, stack = false, close = true, description = "",
        client = { image = "cylind2.png", event = "jim-mechanic:client:applyExtraPart", level = 2, mod = "cylind", remove = false },
    },
    ['cylind3'] = {
        label = "Tier 3 Cylinder Head", weight = 0, stack = false, close = true, description = "",
        client = { image = "cylind3.png", event = "jim-mechanic:client:applyExtraPart", level = 3, mod = "cylind", remove = false },
    },

    ['cables1'] = {
        label = "Tier 1 Battery Cables", weight = 0, stack = false, close = true, description = "",
        client = { image = "cables1.png", event = "jim-mechanic:client:applyExtraPart", level = 1, mod = "cables", remove = false },
    },
    ['cables2'] = {
        label = "Tier 2 Battery Cables", weight = 0, stack = false, close = true, description = "",
        client = { image = "cables2.png", event = "jim-mechanic:client:applyExtraPart", level = 2, mod = "cables", remove = false },
    },
    ['cables3'] = {
        label = "Tier 3 Battery Cables", weight = 0, stack = false, close = true, description = "",
        client = { image = "cables3.png", event = "jim-mechanic:client:applyExtraPart", level = 3, mod = "cables", remove = false },
    },

    ['fueltank1'] = {
        label = "Tier 1 Fuel Tank", weight = 0, stack = false, close = true, description = "",
        client = { image = "fueltank1.png", event = "jim-mechanic:client:applyExtraPart", level = 1, mod = "fueltank", remove = false },
    },
    ['fueltank2'] = {
        label = "Tier 2 Fuel Tank", weight = 0, stack = false, close = true, description = "",
        client = { image = "fueltank2.png", event = "jim-mechanic:client:applyExtraPart", level = 2, mod = "fueltank", remove = false },
    },
    ['fueltank3'] = {
        label = "Tier 3 Fuel Tank", weight = 0, stack = false, close = true, description = "",
        client = { image = "fueltank3.png", event = "jim-mechanic:client:applyExtraPart", level = 3, mod = "fueltank", remove = false },
    },

    ['antilag'] = {
        label = "AntiLag", weight = 0, stack = false, close = true, description = "",
        client = { image = "antiLag.png", event = "jim-mechanic:client:applyAntiLag", remove = false },
    },

    ['underglow_controller'] = {
        label = "Neon Controller", weight = 0, stack = false, close = true, description = "",
        client = { image = "underglow_controller.png", event = "jim-mechanic:client:neonMenu", },
    },
    ['headlights'] = {
        label = "Xenon Headlights", weight = 0, stack = false, close = true, description = "",
        client = { image = "headlights.png", event = "jim-mechanic:client:applyXenons", },
    },

    ['tint_supplies'] = {
        label = "Window Tint Kit", weight = 0, stack = false, close = true, description = "",
        client = { image = "tint_supplies.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },

    ['customplate'] = {
        label = "Customized Plates", weight = 0, stack = false, close = true, description = "",
        client = { image = "plate.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['hood'] = {
        label = "Vehicle Hood", weight = 0, stack = false, close = true, description = "",
        client = { image = "hood.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['roof'] = {
        label = "Vehicle Roof", weight = 0, stack = false, close = true, description = "",
        client = { image = "roof.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['spoiler'] = {
        label = "Vehicle Spoiler", weight = 0, stack = false, close = true, description = "",
        client = { image = "spoiler.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['bumper'] = {
        label = "Vehicle Bumper", weight = 0, stack = false, close = true, description = "",
        client = { image = "bumper.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['skirts'] = {
        label = "Vehicle Skirts", weight = 0, stack = false, close = true, description = "",
        client = { image = "skirts.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['exhaust'] = {
        label = "Vehicle Exhaust", weight = 0, stack = false, close = true, description = "",
        client = { image = "exhaust.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['seat'] = {
        label = "Seat Cosmetics", weight = 0, stack = false, close = true, description = "",
        client = { image = "seat.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['rollcage'] = {
        label = "Roll Cage", weight = 0, stack = false, close = true, description = "",
        client = { image = "rollcage.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },

    ['rims'] = {
        label = "Custom Wheel Rims", weight = 0, stack = false, close = true, description = "",
        client = { image = "rims.png", event = "jim-mechanic:client:Rims:Check", },
    },

    ['livery'] = {
        label = "Livery Roll", weight = 0, stack = false, close = true, description = "",
        client = { image = "livery.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['paintcan'] = {
        label = "Vehicle Spray Can", weight = 0, stack = false, close = true, description = "",
        client = { image = "spraycan.png", event = "jim-mechanic:client:Paints:Check", },
    },
    ['tires'] = {
        label = "Drift Smoke Tires", weight = 0, stack = false, close = true, description = "",
        client = { image = "tires.png", event = "jim-mechanic:client:Tires:Check", },
    },

    ['horn'] = {
        label = "Custom Vehicle Horn", weight = 0, stack = false, close = true, description = "",
        client = { image = "horn.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },

    ['internals'] = {
        label = "Internal Cosmetics", weight = 0, stack = false, close = true, description = "",
        client = { image = "internals.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },
    ['externals'] = {
        label = "Exterior Cosmetics", weight = 0, stack = false, close = true, description = "",
        client = { image = "mirror.png", event = "jim-mechanic:client:Cosmetic:Check", },
    },

    ['newoil'] = {
        label = "Car Oil", weight = 0, stack = false, close = true, description = "",
        client = { image = "caroil.png", },
    },
    ['sparkplugs'] = {
        label = "Spark Plugs", weight = 0, stack = false, close = true, description = "",
        client = { image = "sparkplugs.png", },
    },
    ['carbattery'] = {
        label = "Car Battery", weight = 0, stack = false, close = true, description = "",
        client = { image = "carbattery.png", },
    },
    ['axleparts'] = {
        label = "Axle Parts", weight = 0, stack = false, close = true, description = "",
        client = { image = "axleparts.png", },
    },
    ['sparetire'] = {
        label = "Spare Tire", weight = 0, stack = false, close = true, description = "",
        client = { image = "sparetire.png", event = "jim-mechanic:client:wheelRepair" },
    },

    ['harness'] = {
        label = "Race Harness", weight = 0, stack = true, close = true, description = "Racing Harness so no matter what you stay in the car",
        client = { image = "harness.png", event = "jim-mechanic:client:applyHarness", remove = false },
    },

    ['manual'] = {
        label = "Manual Transmission", weight = 0, stack = true, close = true, description = "Manual Transmission change for vehicles",
        client = { image = "manual.png", event = "jim-mechanic:client:applyManual", remove = false },
    },

    ['underglow'] = {
        label = "Underglow LEDS", weight = 0, stack = true, close = true, description = "Underglow addition for vehicles",
        client = { image = "underglow.png", event = "jim-mechanic:client:applyUnderglow", remove = false },
    },

    ['stancerkit'] = {
        label = "Stancer Kit", weight = 0, stack = true, close = true, description = "Stancer Kit for vehicles",
        client = { image = "stancerkit.png", event = "jim-mechanic:client:stancerMenu", remove = false },
    },

    ['newplate'] = {
        label = "New Plate", weight = 250, stack = false, close = true, description = "A Customizable licence plate.",
        client = { image = "newplate.png", event = "jim-mechanic:client:setplate:Menu" }
    },

    -- Replace these if these are already installed

    --['cleaningkit'] = {
    --    label = "Cleaning Kit", weight = 0, stack = true, close = true, description = "A microfiber cloth with some soap will let your car sparkle again!",
    --    client = { image = "cleaningkit.png", event = "jim-mechanic:client:cleanVehicle"},
    --},
    --['repairkit'] = {
    --    label = "Repairkit", weight = 0, stack = true, close = true, description = "A nice toolbox with stuff to repair your vehicle",
    --    client = { image = "repairkit.png", event = "jim-mechanic:vehFailure:RepairVehicle", item = "repairkit", full = false },
    --},
    --['advancedrepairkit'] = {
    --    label = "Advanced Repairkit", weight = 0, stack = true, close = true, description = "A nice toolbox with stuff to repair your vehicle",
    --    client = { image = "advancedkit.png", event = "jim-mechanic:vehFailure:RepairVehicle", item = "advancedrepairkit", full = true },
    --},

    -- BurgerShot Items 
    ['bg_bleeder_burger'] = {
        label = 'Bleeder Burger',
        weight = 220,
        stack = true,
        close = true,
        consume = 1,
        description = 'A delicious burger from Burger Shot.',
        client = {
            status = { hunger = 50 },
            anim = { dict = 'mp_player_inteat@burger', clip = 'mp_player_int_eat_burger' },
            prop = {
                model = 'prop_cs_burger_01',
                pos = { x = 0.13, y = 0.05, z = 0.02 },
                rot = { x = 160.0, y = 6.0, z = 60.0 },
                bone = 18905
            },
            usetime = 6500,
        }
    },
    ['bg_fingle_burger'] = {
        label = 'Fingle Burger',
        weight = 220,
        stack = true,
        close = true,
        consume = 1,
        description = 'A tasty burger from Burger Shot.',
        client = {
            status = { hunger = 45 },
            anim = { dict = 'mp_player_inteat@burger', clip = 'mp_player_int_eat_burger' },
            prop = {
                model = 'prop_cs_burger_01',
                pos = { x = 0.13, y = 0.05, z = 0.02 },
                rot = { x = 160.0, y = 6.0, z = 60.0 },
                bone = 18905
            },
            usetime = 6500,
        }
    },
    ['bg_heart_stopper_burger'] = {
        label = 'Heart Stopper Burger',
        weight = 220,
        stack = true,
        close = true,
        consume = 1,
        description = 'A heart-stopping burger from Burger Shot.',
        client = {
            status = { hunger = 60 },
            anim = { dict = 'mp_player_inteat@burger', clip = 'mp_player_int_eat_burger' },
            prop = {
                model = 'prop_cs_burger_01',
                pos = { x = 0.13, y = 0.05, z = 0.02 },
                rot = { x = 160.0, y = 6.0, z = 60.0 },
                bone = 18905
            },
            usetime = 6500,
        }
    },
    ['bg_meat_free_burger'] = {
        label = 'Meat Free Burger',
        weight = 220,
        stack = true,
        close = true,
        consume = 1,
        description = 'A vegetarian burger from Burger Shot.',
        client = {
            status = { hunger = 40 },
            anim = { dict = 'mp_player_inteat@burger', clip = 'mp_player_int_eat_burger' },
            prop = {
                model = 'prop_cs_burger_01',
                pos = { x = 0.13, y = 0.05, z = 0.02 },
                rot = { x = 160.0, y = 6.0, z = 60.0 },
                bone = 18905
            },
            usetime = 6500,
        }
    },
    ['bg_money_shot_burger'] = {
        label = 'Money Shot Burger',
        weight = 220,
        stack = true,
        close = true,
        consume = 1,
        description = 'A premium burger from Burger Shot.',
        client = {
            status = { hunger = 55 },
            anim = { dict = 'mp_player_inteat@burger', clip = 'mp_player_int_eat_burger' },
            prop = {
                model = 'prop_cs_burger_01',
                pos = { x = 0.13, y = 0.05, z = 0.02 },
                rot = { x = 160.0, y = 6.0, z = 60.0 },
                bone = 18905
            },
            usetime = 6500,
        }
    },
    ['bg_burgershot_fries'] = {
        label = 'Burger Shot Fries',
        weight = 120,
        stack = true,
        close = true,
        consume = 1,
        description = 'Crispy fries from Burger Shot.',
        client = {
            status = { hunger = 30 },
            anim = { dict = 'mp_player_inteat@burger', clip = 'mp_player_int_eat_burger' },
            prop = {
                model = 'prop_food_bs_chips',
                pos = { x = 0.13, y = -0.06, z = 0.1 },
                rot = { x = -120.0, y = -50.0, z = 0.0 },
                bone = 18905
            },
            usetime = 4500,
        }
    },
    ['bg_ecola'] = {
        label = 'eCola',
        weight = 350,
        stack = true,
        close = true,
        consume = 1,
        description = 'Refreshing eCola soda.',
        client = {
            status = { thirst = 40 },
            anim = { dict = 'bremote@milchake', clip = 'milkshake_idle_clip' },
            prop = {
                model = 'prop_rpemotes_soda03',
                pos = { x = 0.0470, y = 0.0040, z = -0.0600 },
                rot = { x = -88.0263, y = -25.0367, z = -27.3898 },
                bone = 28422
            },
            usetime = 3500,
        }
    },
    ['bg_ecola_light'] = {
        label = 'eCola Light',
        weight = 350,
        stack = true,
        close = true,
        consume = 1,
        description = 'Refreshing eCola Light soda with zero calories.',
        client = {
            status = { thirst = 40 },
            anim = { dict = 'bremote@milchake', clip = 'milkshake_idle_clip' },
            prop = {
                model = 'prop_rpemotes_soda03',
                pos = { x = 0.0470, y = 0.0040, z = -0.0600 },
                rot = { x = -88.0263, y = -25.0367, z = -27.3898 },
                bone = 28422
            },
            usetime = 3500,
        }
    },
    ['bg_sprunk'] = {
        label = 'Sprunk',
        weight = 350,
        stack = true,
        close = true,
        consume = 1,
        description = 'Refreshing Sprunk soda.',
        client = {
            status = { thirst = 40 },
            anim = { dict = 'bremote@milchake', clip = 'milkshake_idle_clip' },
            prop = {
                model = 'prop_rpemotes_soda01',
                pos = { x = 0.0470, y = 0.0040, z = -0.0600 },
                rot = { x = -88.0263, y = -25.0367, z = -27.3898 },
                bone = 28422
            },
            usetime = 3500,
        }
    },
    ['bg_drang_o_tang'] = {
        label = 'Drang-O-Tang',
        weight = 350,
        stack = true,
        close = true,
        consume = 1,
        description = 'Refreshing Drang-O-Tang soda.',
        client = {
            status = { thirst = 40 },
            anim = { dict = 'bremote@milchake', clip = 'milkshake_idle_clip' },
            prop = {
                model = 'prop_rpemotes_soda02',
                pos = { x = 0.0470, y = 0.0040, z = -0.0600 },
                rot = { x = -88.0263, y = -25.0367, z = -27.3898 },
                bone = 28422
            },
            usetime = 3500,
        }
    },
    ['bg_juice_orange'] = {
        label = 'Orange Juice',
        weight = 350,
        stack = true,
        close = true,
        consume = 1,
        description = 'Fresh orange juice.',
        client = {
            status = { thirst = 45 },
            anim = { dict = 'bremote@milchake', clip = 'milkshake_idle_clip' },
            prop = {
                model = 'prop_rpemotes_juice01',
                pos = { x = 0.0470, y = 0.0040, z = -0.0600 },
                rot = { x = -88.0263, y = -25.0367, z = -27.3898 },
                bone = 28422
            },
            usetime = 3500,
        }
    },
    ['bg_juice_apple'] = {
        label = 'Apple Juice',
        weight = 350,
        stack = true,
        close = true,
        consume = 1,
        description = 'Fresh apple juice.',
        client = {
            status = { thirst = 45 },
            anim = { dict = 'bremote@milchake', clip = 'milkshake_idle_clip' },
            prop = {
                model = 'prop_rpemotes_juice01',
                pos = { x = 0.0470, y = 0.0040, z = -0.0600 },
                rot = { x = -88.0263, y = -25.0367, z = -27.3898 },
                bone = 28422
            },
            usetime = 3500,
        }
    },
    ['bg_juice_pineapple'] = {
        label = 'Pineapple Juice',
        weight = 350,
        stack = true,
        close = true,
        consume = 1,
        description = 'Fresh pineapple juice.',
        client = {
            status = { thirst = 45 },
            anim = { dict = 'bremote@milchake', clip = 'milkshake_idle_clip' },
            prop = {
                model = 'prop_rpemotes_juice01',
                pos = { x = 0.0470, y = 0.0040, z = -0.0600 },
                rot = { x = -88.0263, y = -25.0367, z = -27.3898 },
                bone = 28422
            },
            usetime = 3500,
        }
    },
    ['bg_bacon'] = {
        label = "Bacon",
        weight = 500,
        stack = true,
        close = true,
        description = "Savory crispy strips of meat.",
    },
    ['bg_bun'] = {
        label = "Bun",
        weight = 250,
        stack = true,
        close = true,
        description = "Soft and fluffy bread base.",
    },
    ['bg_empty_bag'] = {
        label = "Empty Bag",
        weight = 250,
        stack = false,
        close = true,
        consume = 1,
        description = "Empty bag.",
    },
    ['bg_burgershot_bag'] = {
        label = "Burgershot Bag",
        weight = 5000,
        stack = false,
        close = true,
        consume = 1,
        description = "Convenient packaging for your meal.",
    },
    ['bg_knife'] = {
        label = "Knife",
        weight = 500,
        stack = false,
        close = true,
        description = "Knife to cut ingredient.",
    },
    ['bg_cheese'] = {
        label = "Cheese",
        weight = 150,
        stack = true,
        close = true,
        description = "Melted and creamy dairy delight.",
    },
    ['bg_ice'] = {
        label = "Ice",
        weight = 100,
        stack = true,
        close = true,
        description = "Chilled and refreshing frozen water.",
    },
    ['bg_oil'] = {
        label = "Oil",
        weight = 300,
        stack = true,
        close = true,
        description = "Essential cooking and flavoring ingredient.",
    },
    ['bg_fried_egg'] = {
        label = "Fried Egg",
        weight = 300,
        stack = true,
        close = true,
        description = "Delightfully cooked sunny-side-up egg.",
    },
    ['bg_egg'] = {
        label = "Egg",
        weight = 300,
        stack = true,
        close = true,
        description = "Versatile and protein-rich breakfast staple.",
    },
    ['bg_cooked_patty'] = {
        label = "Cooked Patty",
        weight = 300,
        stack = true,
        close = true,
        description = "Perfectly grilled meat patty.",
    },
    ['bg_raw_patty'] = {
        label = "Raw Patty",
        weight = 250,
        stack = true,
        close = true,
        description = "Uncooked meat patty ready for grilling.",
    },
    ['bg_ecola_empty'] = {
        label = "eCola Empty Cup",
        weight = 250,
        stack = true,
        close = true,
        description = "Empty Cup.",
    },
    ['bg_ecola_light_empty'] = {
        label = "eCola Light Empty Cup",
        weight = 250,
        stack = true,
        close = true,
        description = "Empty Cup.",
    },
    ['bg_drang_o_tang'] = {
        label = "Drang O Tang",
        weight = 1000,
        stack = true,
        close = true,
        consume = 1,
        description = "Refreshing carbonated beverage.",
    },
    ['bg_drang_o_tang_empty'] = {
        label = "DT Empty Cup",
        weight = 250,
        stack = true,
        close = true,
        description = "Empty Cup.",
    },
    ['bg_sprunk_empty'] = {
        label = "Sprunk Empty Cup",
        weight = 250,
        stack = true,
        close = true,
        description = "Empty Cup.",
    },
    ['bg_water_bootle'] = {
        label = "Water Bootle",
        weight = 1000,
        stack = true,
        close = true,
        consume = 1,
        description = "Refreshing hydration in a convenient portable container."
    },
    ['bg_juice_empty'] = {
        label = "Juice Empty Cup",
        weight = 500,
        stack = true,
        close = true,
        description = "Cup for drinks.",
    },
    ['bg_destroyed_cup'] = {
        label = "Destroy Cup",
        weight = 200,
        stack = true,
        close = true,
        description = "Destroy cup for drinks.",
    },
    ['bg_salt'] = {
        label = "Salt",
        weight = 250,
        stack = true,
        close = true,
        description = "Essential seasoning for enhancing flavors.",
    },
    ['bg_ketchup'] = {
        label = "Ketchup",
        weight = 250,
        stack = true,
        close = true,
        description = "Classic tangy tomato-based condiment.",
    },
    ['bg_special_sauce'] = {
        label = "Special Sauce",
        weight = 250,
        stack = true,
        close = true,
        description = "Secret recipe dressing for added flavor.",
    },
    ['bg_chopped_potato'] = {
        label = "Chopped Potato",
        weight = 150,
        stack = true,
        close = true,
        description = "Bite-sized pieces of fried potatoes.",
    },
    ['bg_chopped_fries'] = {
        label = "Chopped Fries",
        weight = 150,
        stack = true,
        close = true,
        description = "Chopped fries.",
    },
    ['bg_chopped_lettuce'] = {
        label = "Chopped Lettuce",
        weight = 50,
        stack = true,
        close = true,
        description = "Fresh and crisp leafy greens.",
    },
    ['bg_chopped_onion'] = {
        label = "Chopped Onion",
        weight = 50,
        stack = true,
        close = true,
        description = "Finely diced pungent bulb vegetable.",
    },
    ['bg_chopped_pickle'] = {
        label = "Chopped Pickle",
        weight = 50,
        stack = true,
        close = true,
        description = "Tangy and crunchy brined cucumber.",
    },
    ['bg_chopped_tomato'] = {
        label = "Chopped Tomato",
        weight = 50,
        stack = true,
        close = true,
        description = "Ripe and juicy red fruit.",
    },
    ['bg_lettuce'] = {
        label = "Lettuce",
        weight = 500,
        stack = true,
        close = true,
        description = "Crisp and leafy green vegetable.",
    },
    ['bg_onion'] = {
        label = "Onion",
        weight = 250,
        stack = true,
        close = true,
        description = "Aromatic and flavorful bulb vegetable.",
    },
    ['bg_pickle'] = {
        label = "Pickle",
        weight = 250,
        stack = true,
        close = true,
        description = "Tangy and briny pickled cucumber.",
    },
    ['bg_potato'] = {
        label = "Potato",
        weight = 250,
        stack = true,
        close = true,
        description = "Versatile starchy vegetable for various dishes.",
    },
    ['bg_tomato'] = {
        label = "Tomato",
        weight = 250,
        stack = true,
        close = true,
        description = "Juicy and vibrant red fruit.",
    },
    ['bg_burgershot_fries_box'] = {
        label = "Burgershot Box Fries",
        weight = 500,
        stack = true,
        close = true,
        description = "Box fries.",
    },
    ['bg_10litr_water'] = {
        label = "10ltr Water",
        weight = 10000,
        stack = false,
        close = true,
        description = "10 litr water.",
    },
    ['bg_10litr_apple'] = {
        label = "10ltr Apple",
        weight = 10000,
        stack = false,
        close = true,
        description = "10 litr Apple.",
    },
    ['bg_10litr_ecola'] = {
        label = "10ltr eCola",
        weight = 10000,
        stack = false,
        close = true,
        description = "10 litr eCola.",
    },
    ['bg_10litr_ecola_light'] = {
        label = "10ltr eCola Light",
        weight = 10000,
        stack = false,
        close = true,
        description = "10 litr eCola Light.",
    },
    ['bg_10litr_drang_o_tang'] = {
        label = "10ltr Drang O Tang",
        weight = 10000,
        stack = false,
        close = true,
        description = "10 litr Drang O Tang.",
    },
    ['bg_10litr_orange'] = {
        label = "10ltr Orange",
        weight = 10000,
        stack = false,
        close = true,
        description = "10 litr Orange.",
    },
    ['bg_10litr_sprunk'] = {
        label = "10ltr Sprunk",
        weight = 10000,
        stack = false,
        close = true,
        description = "10 litr Sprunk.",
    },
    ['bg_10litr_pineapple'] = {
        label = "10ltr Pine Apple",
        weight = 10000,
        stack = false,
        close = true,
        description = "10 litr Pine Apple.",
    },
    ['bg_sticker'] = {
        label = "BurgerShot Sticker",
        weight = 20,
        stack = true,
        close = true,
        description = "Mini sticker using in packaging.",
    },

    -- Farming Items

    ['tomato_seed'] = {
        label = 'Tomato Seed',
        weight = 200,
        stack = true,
        close = true,
        description = 'A TOMATO SEED',
        client = { image = 'tomato_seed.png' }
    },
    
    ['tomato'] = {
        label = 'Tomato',
        weight = 200,
        stack = true,
        close = false,
        description = 'A TOMATO',
        client = { image = 'tomato.png' }
    },
    
    ['fertilizer'] = {
        label = 'Fertilizer',
        weight = 200,
        stack = true,
        close = true,
        description = 'FERTILIZER FOR SHIIITS',
        client = { image = 'weed_nutrition.png' }
    },
    
    ['cabbage_seed'] = {
        label = 'Cabbage Seed',
        weight = 200,
        stack = true,
        close = true,
        description = 'A CABBAGE SEED',
        client = { image = 'cabbage_seed.png' }
    },
    
    ['cabbage'] = {
        label = 'Cabbage',
        weight = 200,
        stack = true,
        close = false,
        description = 'A CABBAGE',
        client = { image = 'cabbage.png' }
    },
    
    ['pumpkin_seed'] = {
        label = 'Pumpkin Seed',
        weight = 200,
        stack = true,
        close = true,
        description = 'A PUMPKIN SEED',
        client = { image = 'pumpkin_seed.png' }
    },
    
    ['pumpkin'] = {
        label = 'Pumpkin',
        weight = 200,
        stack = true,
        close = false,
        description = 'A PUMPKIN',
        client = { image = 'pumpkin.png' }
    },
    
    ['mushroom'] = {
        label = 'Mushroom',
        weight = 200,
        stack = true,
        close = false,
        description = 'A MUSHROOM',
        client = { image = 'mushroom.png' }
    },
    
    ['apple'] = {
        label = 'Apple',
        weight = 200,
        stack = true,
        close = false,
        description = 'AN APPLE',
        client = { image = 'apple.png' }
    },
    
    ['orange'] = {
        label = 'Orange',
        weight = 200,
        stack = true,
        close = false,
        description = 'AN ORANGE',
        client = { image = 'orange.png' }
    },
    
    ['pig'] = {
        label = 'Pigmeat',
        weight = 200,
        stack = true,
        close = false,
        description = 'Some freshly slaughtered pig meat',
        client = { image = 'pig.png' }
    },
    
    ['cow'] = {
        label = 'Cowmeat',
        weight = 200,
        stack = true,
        close = false,
        description = 'Some freshly slaughtered beef',
        client = { image = 'cowmeat.png' }
    },
    
    ['hen'] = {
        label = 'Chicken',
        weight = 200,
        stack = true,
        close = false,
        description = 'Some fresh chicken meat',
        client = { image = 'chicken.png' }
    },
    
    ['milk'] = {
        label = 'Milk',
        weight = 200,
        stack = true,
        close = false,
        description = 'A warm, freshly milked high calcium milk',
        client = { image = 'milk.png' }
    },
    
    ['wheat'] = {
        label = 'Wheat',
        weight = 200,
        stack = true,
        close = false,
        description = 'Fresh from the grounds',
        client = { image = 'wheat.png' }
    },   

    -- tiger Custom

    ['postalbox'] = {
          label = 'Postalbox',
          weight = 5,
          stack = true,
          close = true,
          description = 'Postal Box'
        },

        ['postal_recycle'] = {
          label = 'Postal Recycle',
          weight = 5,
          stack = true,
          close = true,
          description = 'Postal Recycle'
        },

        ['garbagebox'] = {
          label = "Garbage box",
          weight = 5,
          stack = true,
          close = true,
          description = 'Garbage box'
        },

        ['garbage_recycle'] = {
          label = 'Garbage Recycle',
          weight = 5,
          stack = true,
          close = true,
          description = 'Garbage Recycle'
        },
        ['fishbox'] = {
          label = 'Fish Box',
          weight = 5,
          stack = true,
          close = true,
          description = 'Fish Box'
        },
        ['fish_recycle'] = {
          label = 'Fish Recycle',
          weight = 5,
          stack = true,
          close = true,
          description = 'Fish Recycle'
        },
        ['electricianbox'] = {
          label = 'Electronic Box',
          weight = 5,
          stack = true,
          close = true,
          description = 'Electronic Box'
        },
        ['electrician_recycle'] = {
          label = 'Electronic Recycle',
          weight = 5,
          stack = true,
          close = true,
          description = 'Electronic Recycle'
        },
        ['plumberbox'] = {
          label = 'Plumber Box',
          weight = 5,
          stack = true,
          close = true,
          description = 'Plumber Box'
        },
        ['plumber_recycle'] = {
          label = 'Plumber Recycle',
          weight = 5,
          stack = true,
          close = true,
          description = 'Plumber Recycle'
        },
    
    -- Tailor Items

    ['acrylic'] = {
        label = 'Acrylic',
        weight = 100,
        stack = true,
        close = true,
        description = 'Acrylic'
    },
    ['binco_basic_blazer'] = {
        label = 'Basic Blazer',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Blazer'
    },
    ['binco_basic_blouse'] = {
        label = 'Basic Blouse',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Blouse'
    },
    ['binco_basic_bralet'] = {
        label = 'Basic Bralet',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Bralet'
    },
    ['binco_basic_jean'] = {
        label = 'Basic Jean',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Jean'
    },
    ['binco_basic_jumpsuit'] = {
        label = 'Basic Jumpsuit',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Jumpsuit'
    },
    ['binco_basic_shirt'] = {
        label = 'Basic Shirt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Shirt'
    },
    ['binco_basic_short'] = {
        label = 'Basic Short',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Short'
    },
    ['binco_basic_singlet'] = {
        label = 'Basic Singlet',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Singlet'
    },
    ['binco_basic_sweatshirt'] = {
        label = 'Basic Sweatshirt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Sweatshirt'
    },
    ['binco_basic_tshirt'] = {
        label = 'Basic Tshirt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Tshirt'
    },
    ['binco_basic_vest'] = {
        label = 'Basic Vest',
        weight = 100,
        stack = true,
        close = true,
        description = 'Basic Vest'
    },
    ['cashmere'] = {
        label = 'Cashmere',
        weight = 100,
        stack = true,
        close = true,
        description = 'Cashmere'
    },
    ['cotton'] = {
        label = 'Cotton',
        weight = 100,
        stack = true,
        close = true,
        description = 'Cotton'
    },
    ['cupro'] = {
        label = 'Cupro',
        weight = 100,
        stack = true,
        close = true,
        description = 'Cupro'
    },
    ['fiber'] = {
        label = 'Fiber',
        weight = 100,
        stack = true,
        close = true,
        description = 'Fiber'
    },
    ['hemp'] = {
        label = 'Hemp',
        weight = 100,
        stack = true,
        close = true,
        description = 'Hemp'
    },
    ['iron'] = {
        label = 'Iron',
        weight = 100,
        stack = true,
        close = true,
        description = 'Iron'
    },
    ['jute'] = {
        label = 'Jute',
        weight = 100,
        stack = true,
        close = true,
        description = 'Jute'
    },
    ['knitting_wheel'] = {
        label = 'Knitting Wheel',
        weight = 100,
        stack = true,
        close = true,
        description = 'Knitting Wheel'
    },
    ['linen'] = {
        label = 'Linen',
        weight = 100,
        stack = true,
        close = true,
        description = 'Linen'
    },
    ['lyocell'] = {
        label = 'Lyocell',
        weight = 100,
        stack = true,
        close = true,
        description = 'Lyocell'
    },
    ['metal'] = {
        label = 'Metal',
        weight = 100,
        stack = true,
        close = true,
        description = 'Metal'
    },
    ['metal_bobbin'] = {
        label = 'Metal Bobbin',
        weight = 100,
        stack = true,
        close = true,
        description = 'Metal Bobbin'
    },
    ['nubuck'] = {
        label = 'Nubuck',
        weight = 100,
        stack = true,
        close = true,
        description = 'Nubuck'
    },
    ['paint'] = {
        label = 'Paint',
        weight = 100,
        stack = true,
        close = true,
        description = 'Paint'
    },
    ['polyester'] = {
        label = 'Polyester',
        weight = 100,
        stack = true,
        close = true,
        description = 'Polyester'
    },
    ['ponsonbys_belt'] = {
        label = 'Classy Belt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Classy Belt'
    },
    ['ponsonbys_chino'] = {
        label = 'Chino',
        weight = 100,
        stack = true,
        close = true,
        description = 'Chino'
    },
    ['ponsonbys_jacket'] = {
        label = 'Suit Jacket',
        weight = 100,
        stack = true,
        close = true,
        description = 'Suit Jacket'
    },
    ['ponsonbys_shirt'] = {
        label = 'Grey Shirt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Grey Shirt'
    },
    ['ponsonbys_suit'] = {
        label = 'Full Suit',
        weight = 100,
        stack = true,
        close = true,
        description = 'Full Suit'
    },
    ['ponsonbys_tie'] = {
        label = 'Tie',
        weight = 100,
        stack = true,
        close = true,
        description = 'Tie'
    },
    ['ponsonbys_waistcoats'] = {
        label = 'Waistcoats',
        weight = 100,
        stack = true,
        close = true,
        description = 'Waistcoats'
    },
    ['quality_silk'] = {
        label = 'Quality Silk',
        weight = 100,
        stack = true,
        close = true,
        description = 'Quality Silk'
    },
    ['rope_reel'] = {
        label = 'Rope Reel',
        weight = 100,
        stack = true,
        close = true,
        description = 'Rope Reel'
    },
    ['silver'] = {
        label = 'Silver',
        weight = 100,
        stack = true,
        close = true,
        description = 'Silver'
    },
    ['spandex'] = {
        label = 'Spandex',
        weight = 100,
        stack = true,
        close = true,
        description = 'Spandex'
    },
    ['steel_bobbin'] = {
        label = 'Steel Bobbin',
        weight = 100,
        stack = true,
        close = true,
        description = 'Steel Bobbin'
    },
    ['suburban_bralet'] = {
        label = 'Suburban Bralet',
        weight = 100,
        stack = true,
        close = true,
        description = 'Suburban Bralet'
    },
    ['suburban_chain'] = {
        label = 'Chain',
        weight = 100,
        stack = true,
        close = true,
        description = 'Chain'
    },
    ['suburban_dress'] = {
        label = 'Suburban Dress',
        weight = 100,
        stack = true,
        close = true,
        description = 'Suburban Dress'
    },
    ['suburban_jogger'] = {
        label = 'Suburban Jogger',
        weight = 100,
        stack = true,
        close = true,
        description = 'Suburban Jogger'
    },
    ['suburban_shirt'] = {
        label = 'Suburban Shirt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Suburban Shirt'
    },
    ['suburban_short'] = {
        label = 'Suburban Short',
        weight = 100,
        stack = true,
        close = true,
        description = 'Suburban Short'
    },
    ['suburban_skirt'] = {
        label = 'Skirt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Skirt'
    },
    ['suburban_tshirt'] = {
        label = 'Suburban Tshirt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Suburban Tshirt'
    },
    ['suburban_sweatshirt'] = {
        label = 'Suburban Sweatshirt',
        weight = 100,
        stack = true,
        close = true,
        description = 'Suburban Sweatshirt'
    },
    ['thin_needle'] = {
        label = 'Thin Needle',
        weight = 100,
        stack = true,
        close = true,
        description = 'Thin Needle'
    },
    ['wool'] = {
        label = 'Wool Yarn',
        weight = 100,
        stack = true,
        close = true,
        description = 'Wool Yarn'
    },

    -- ID Card
['id_card'] = {
    label = 'ID Card',
    weight = 50,
    stack = false,
    close = true,
    description = 'A card containing all your information to identify yourself.',
    client = {
        image = 'id_card.png',
    },
    buttons = {
        {
            label = 'View ID',
            action = function(slot)
                -- The action is handled by the hook in qb-passport
                return true
            end
        }
    }
},

-- Driver License
['driver_license'] = {
    label = 'Driver License',
    weight = 50,
    stack = false,
    close = true,
    description = 'Permit to operate a vehicle.',
    client = {
        image = 'driver_license.png',
    },
    buttons = {
        {
            label = 'View License',
            action = function(slot)
                -- The action is handled by the hook in qb-passport
                return true
            end
        }
    }
},

-- Weapon License
['weaponlicense'] = {
    label = 'Weapon License',
    weight = 50,
    stack = false,
    close = true,
    description = 'Permit to carry a weapon.',
    client = {
        image = 'weaponlicense.png',
    },
    buttons = {
        {
            label = 'View License',
            action = function(slot)
                -- The action is handled by the hook in qb-passport
                return true
            end
        }
    }
},

-- Lawyer Pass
['lawyerpass'] = {
    label = 'Lawyer Pass',
    weight = 50,
    stack = false,
    close = true,
    description = 'License to practice law.',
    client = {
        image = 'lawyerpass.png',
    },
    buttons = {
        {
            label = 'View Pass',
            action = function(slot)
                -- The action is handled by the hook in qb-passport
                return true
            end
        }
    }
},
    
    -- Fishing Items 

    ['tablet'] = {
		label = 'Tablet Job',
		stack = true,
        close = true,
		weight = 250,
        description = "Tablet Job",
        client = {
            image = "tablet.png",
        },
	},
    ['keyhome'] = {
		label = 'key home',
		stack = true,
        close = true,
		weight = 250,
        description = "Key home",
        client = {
            image = "keyhome.png",
        },
	},
    ['basic_rod'] = {
		label = 'Fishing rod',
		stack = false,
		weight = 250
	},

	['graphite_rod'] = {
		label = 'Graphite rod',
		stack = false,
		weight = 350
	},

	['titanium_rod'] = {
		label = 'Titanium rod',
		stack = false,
		weight = 450
	},

	['worms'] = {
		label = 'Worms',
		weight = 10
	},

	['artificial_bait'] = {
		label = 'Artificial bait',
		weight = 30
	},

	['anchovy'] = {
		label = 'Anchovy',
		weight = 20
	},

	['grouper'] = {
		label = 'Grouper',
		weight = 3500
	},

	['haddock'] = {
		label = 'Haddock',
		weight = 500
	},

	['mahi_mahi'] = {
		label = 'Mahi Mahi',
		weight = 3500
	},

	['piranha'] = {
		label = 'Piranha',
		weight = 1500
	},

	['red_snapper'] = {
		label = 'Red Snapper',
		weight = 2500
	},

	['salmon'] = {
		label = 'Salmon',
		weight = 1000
	},

	['shark'] = {
		label = 'Shark',
		weight = 7500
	},

	['trout'] = {
		label = 'Trout',
		weight = 750
	},

	['tuna'] = {
		label = 'Tuna',
		weight = 10000
	},

    ['clothing'] = {
        label = 'Clothing',
        consume = 1,
    },

    ['money'] = {
        label = 'Money',
    },

    ['black_money'] = {
        label = 'Dirty Money',
    },

    ['radio'] = {
        label = 'Radio',
        weight = 1000,
        stack = false,
        allowArmed = true
    },

    ['advancedlockpick'] = {
        label = 'Advanced Lockpick',
        weight = 500,
    },

    ['screwdriverset'] = {
        label = 'Screwdriver Set',
        weight = 500,
    },

    ['electronickit'] = {
        label = 'Electronic Kit',
        weight = 500,
    },

    ['repairkit'] = {
        label = 'Repair Kit',
        weight = 2500,
    },

    ['advancedrepairkit'] = {
        label = 'Advanced Repair Kit',
        weight = 4000,
    },

    ['diamond_ring'] = {
        label = 'Diamond',
        weight = 1500,
    },

    ['rolex'] = {
        label = 'Golden Watch',
        weight = 1500,
    },

    ['goldbar'] = {
        label = 'Gold Bar',
        weight = 1500,
    },

    ['goldchain'] = {
        label = 'Golden Chain',
        weight = 1500,
    },

    ['crack_baggy'] = {
        label = 'Crack Baggy',
        weight = 100,
    },

    ['cokebaggy'] = {
        label = 'Bag of Coke',
        weight = 100,
    },

    ['coke_brick'] = {
        label = 'Coke Brick',
        weight = 2000,
    },

    ['coke_small_brick'] = {
        label = 'Coke Package',
        weight = 1000,
    },

    ['xtcbaggy'] = {
        label = 'Bag of Ecstasy',
        weight = 100,
    },

    ['meth'] = {
        label = 'Methamphetamine',
        weight = 100,
    },

    ['oxy'] = {
        label = 'Oxycodone',
        weight = 100,
    },

    ['weed_ak47'] = {
        label = 'AK47 2g',
        weight = 200,
    },

    ['weed_ak47_seed'] = {
        label = 'AK47 Seed',
        weight = 1,
    },

    ['weed_skunk'] = {
        label = 'Skunk 2g',
        weight = 200,
    },

    ['weed_skunk_seed'] = {
        label = 'Skunk Seed',
        weight = 1,
    },

    ['weed_amnesia'] = {
        label = 'Amnesia 2g',
        weight = 200,
    },

    ['weed_amnesia_seed'] = {
        label = 'Amnesia Seed',
        weight = 1,
    },

    ['weed_og-kush'] = {
        label = 'OGKush 2g',
        weight = 200,
    },

    ['weed_og-kush_seed'] = {
        label = 'OGKush Seed',
        weight = 1,
    },

    ['weed_white-widow'] = {
        label = 'OGKush 2g',
        weight = 200,
    },

    ['weed_white-widow_seed'] = {
        label = 'White Widow Seed',
        weight = 1,
    },

    ['weed_purple-haze'] = {
        label = 'Purple Haze 2g',
        weight = 200,
    },

    ['weed_purple-haze_seed'] = {
        label = 'Purple Haze Seed',
        weight = 1,
    },

    ['weed_brick'] = {
        label = 'Weed Brick',
        weight = 2000,
    },

    ['weed_nutrition'] = {
        label = 'Plant Fertilizer',
        weight = 2000,
    },

    ['joint'] = {
        label = 'Joint',
        weight = 200,
    },

    ['rolling_paper'] = {
        label = 'Rolling Paper',
        weight = 0,
    },

    ['empty_weed_bag'] = {
        label = 'Empty Weed Bag',
        weight = 0,
    },

    ['ifaks'] = {
        label = 'Individual First Aid Kit',
        weight = 2500,
    },

    ['painkillers'] = {
        label = 'Painkillers',
        weight = 400,
    },

    ['firework1'] = {
        label = '2Brothers',
        weight = 1000,
    },

    ['firework2'] = {
        label = 'Poppelers',
        weight = 1000,
    },

    ['firework3'] = {
        label = 'WipeOut',
        weight = 1000,
    },

    ['firework4'] = {
        label = 'Weeping Willow',
        weight = 1000,
    },

    ['steel'] = {
        label = 'Steel',
        weight = 100,
    },

    ['rubber'] = {
        label = 'Rubber',
        weight = 100,
    },

    ['metalscrap'] = {
        label = 'Metal Scrap',
        weight = 100,
    },

    ['iron'] = {
        label = 'Iron',
        weight = 100,
    },

    ['copper'] = {
        label = 'Copper',
        weight = 100,
    },

    ['aluminium'] = {
        label = 'Aluminium',
        weight = 100,
    },

    ['plastic'] = {
        label = 'Plastic',
        weight = 100,
    },

    ['glass'] = {
        label = 'Glass',
        weight = 100,
    },

    ['gatecrack'] = {
        label = 'Gatecrack',
        weight = 1000,
    },

    ['cryptostick'] = {
        label = 'Crypto Stick',
        weight = 100,
    },

    ['trojan_usb'] = {
        label = 'Trojan USB',
        weight = 100,
    },

    ['toaster'] = {
        label = 'Toaster',
        weight = 5000,
    },

    ['small_tv'] = {
        label = 'Small TV',
        weight = 100,
    },

    ['security_card_01'] = {
        label = 'Security Card A',
        weight = 100,
    },

    ['security_card_02'] = {
        label = 'Security Card B',
        weight = 100,
    },

    ['drill'] = {
        label = 'Drill',
        weight = 5000,
    },

    ['thermite'] = {
        label = 'Thermite',
        weight = 1000,
    },

    ['diving_gear'] = {
        label = 'Diving Gear',
        weight = 30000,
    },

    ['diving_fill'] = {
        label = 'Diving Tube',
        weight = 3000,
    },

    ['antipatharia_coral'] = {
        label = 'Antipatharia',
        weight = 1000,
    },

    ['dendrogyra_coral'] = {
        label = 'Dendrogyra',
        weight = 1000,
    },

    ['jerry_can'] = {
        label = 'Jerrycan',
        weight = 3000,
    },

    ['nitrous'] = {
        label = 'Nitrous',
        weight = 1000,
    },

    ['wine'] = {
        label = 'Wine',
        weight = 500,
    },

    ['grape'] = {
        label = 'Grape',
        weight = 10,
    },

    ['grapejuice'] = {
        label = 'Grape Juice',
        weight = 200,
    },

    ['vodka'] = {
        label = 'Vodka',
        weight = 500,
    },

    ['whiskey'] = {
        label = 'Whiskey',
        weight = 200,
    },

    ['beer'] = {
        label = 'beer',
        weight = 200,
    },

    ['sandwich'] = {
        label = 'beer',
        weight = 200,
    },

    ['walking_stick'] = {
        label = 'Walking Stick',
        weight = 1000,
    },

    ['binoculars'] = {
        label = 'Binoculars',
        weight = 800,
    },

    ['stickynote'] = {
        label = 'Sticky Note',
        weight = 0,
    },

    ['empty_evidence_bag'] = {
        label = 'Empty Evidence Bag',
        weight = 200,
    },

    ['filled_evidence_bag'] = {
        label = 'Filled Evidence Bag',
        weight = 200,
    },

    ['handcuffs'] = {
        label = 'Handcuffs',
        weight = 200,
    },

	['veh_plates'] = {
		label = "Plates",
		weight = 1000,
		stack = true,
		close = true,
		description = "Install vehicle plates",
		client = {
			image = "veh_plates.png",
		}
	},

	['pinger'] = {
		label = "Pinger",
		weight = 1000,
		stack = true,
		close = true,
		description = "With a pinger and your phone you can send out your location",
		client = {
			image = "pinger.png",
		}
	},

	['weed_purplehaze'] = {
		label = "Purple Haze 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g Purple Haze",
		client = {
			image = "weed_baggy.png",
		}
	},

	['woodcamo_attachment'] = {
		label = "Woodland Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A woodland camo for a weapon",
		client = {
			image = "woodcamo_attachment.png",
		}
	},

	['tosti'] = {
		label = "Grilled Cheese Sandwich",
		weight = 200,
		stack = true,
		close = true,
		description = "Nice to eat",
		client = {
			image = "tosti.png",
		}
	},

	['kurkakola'] = {
		label = "Cola",
		weight = 500,
		stack = true,
		close = true,
		description = "For all the thirsty out there",
		client = {
			image = "cola.png",
		}
	},

	['newsmic'] = {
		label = "News Microphone",
		weight = 100,
		stack = false,
		close = true,
		description = "A microphone for the news",
		client = {
			image = "newsmic.png",
		}
	},

	['venomkey'] = {
		label = "Hotel Key",
		weight = 1,
		stack = false,
		close = true,
		description = "key for a hotel",
		client = {
			image = "key1.png",
		}
	},

	['ironoxide'] = {
		label = "Iron Powder",
		weight = 100,
		stack = true,
		close = false,
		description = "Some powder to mix with.",
		client = {
			image = "ironoxide.png",
		}
	},

	['holoscope_attachment'] = {
		label = "Holo Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A holo scope for a weapon",
		client = {
			image = "holoscope_attachment.png",
		}
	},

	['aluminum'] = {
		label = "Aluminium",
		weight = 100,
		stack = true,
		close = false,
		description = "Nice piece of metal that you can probably use for something",
		client = {
			image = "aluminum.png",
		}
	},

	['veh_exterior'] = {
		label = "Exterior",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle exterior",
		client = {
			image = "veh_exterior.png",
		}
	},

	['armor'] = {
		label = "Armor",
		weight = 5000,
		stack = true,
		close = true,
		description = "Some protection won't hurt... right?",
		client = {
			image = "armor.png",
		}
	},

	['weed_purplehaze_seed'] = {
		label = "Purple Haze Seed",
		weight = 0,
		stack = true,
		close = true,
		description = "A weed seed of Purple Haze",
		client = {
			image = "weed_seed.png",
		}
	},

	['nvscope_attachment'] = {
		label = "Night Vision Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A night vision scope for a weapon",
		client = {
			image = "nvscope_attachment.png",
		}
	},

	['comp_attachment'] = {
		label = "Compensator",
		weight = 1000,
		stack = true,
		close = true,
		description = "A compensator for a weapon",
		client = {
			image = "comp_attachment.png",
		}
	},

	['veh_xenons'] = {
		label = "Xenons",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle xenons",
		client = {
			image = "veh_xenons.png",
		}
	},

	['diamond'] = {
		label = "Diamond",
		weight = 1000,
		stack = true,
		close = true,
		description = "A diamond seems like the jackpot to me!",
		client = {
			image = "diamond.png",
		}
	},

	['sessantacamo_attachment'] = {
		label = "Sessanta Nove Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A sessanta nove camo for a weapon",
		client = {
			image = "sessantacamo_attachment.png",
		}
	},

	['radioscanner'] = {
		label = "Radio Scanner",
		weight = 1000,
		stack = true,
		close = true,
		description = "With this you can get some police alerts. Not 100% effective however",
		client = {
			image = "radioscanner.png",
		}
	},

	['veh_suspension'] = {
		label = "Suspension",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle suspension",
		client = {
			image = "veh_suspension.png",
		}
	},

	['tirerepairkit'] = {
		label = "Tire Repair Kit",
		weight = 1000,
		stack = true,
		close = true,
		description = "A kit to repair your tires",
		client = {
			image = "tirerepairkit.png",
		}
	},

	['newsbmic'] = {
		label = "Boom Microphone",
		weight = 100,
		stack = false,
		close = true,
		description = "A Useable BoomMic",
		client = {
			image = "newsbmic.png",
		}
	},

	['veh_brakes'] = {
		label = "Brakes",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle brakes",
		client = {
			image = "veh_brakes.png",
		}
	},

	['veh_neons'] = {
		label = "Neons",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle neons",
		client = {
			image = "veh_neons.png",
		}
	},

	['water_bottle'] = {
		label = "Bottle of Water",
		weight = 500,
		stack = true,
		close = true,
		description = "For all the thirsty out there",
		client = {
			image = "water_bottle.png",
		}
	},

	['suppressor_attachment'] = {
		label = "Suppressor",
		weight = 1000,
		stack = true,
		close = true,
		description = "A suppressor for a weapon",
		client = {
			image = "suppressor_attachment.png",
		}
	},

	['weed_whitewidow_seed'] = {
		label = "White Widow Seed",
		weight = 0,
		stack = true,
		close = false,
		description = "A weed seed of White Widow",
		client = {
			image = "weed_seed.png",
		}
	},

	['split_end_muzzle_brake'] = {
		label = "Split End Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "split_end_muzzle_brake.png",
		}
	},

	['weed_whitewidow'] = {
		label = "White Widow 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g White Widow",
		client = {
			image = "weed_baggy.png",
		}
	},

	['veh_toolbox'] = {
		label = "Toolbox",
		weight = 1000,
		stack = true,
		close = true,
		description = "Check vehicle status",
		client = {
			image = "veh_toolbox.png",
		}
	},

	['fat_end_muzzle_brake'] = {
		label = "Fat End Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "fat_end_muzzle_brake.png",
		}
	},

	['weed_ogkush_seed'] = {
		label = "OGKush Seed",
		weight = 0,
		stack = true,
		close = true,
		description = "A weed seed of OG Kush",
		client = {
			image = "weed_seed.png",
		}
	},

	['moneybag'] = {
		label = "Money Bag",
		weight = 0,
		stack = false,
		close = true,
		description = "A bag with cash",
		client = {
			image = "moneybag.png",
		}
	},

	['veh_armor'] = {
		label = "Armor",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle armor",
		client = {
			image = "veh_armor.png",
		}
	},

	['heavy_duty_muzzle_brake'] = {
		label = "HD Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "heavy_duty_muzzle_brake.png",
		}
	},

	['slanted_muzzle_brake'] = {
		label = "Slanted Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "slanted_muzzle_brake.png",
		}
	},

	['veh_transmission'] = {
		label = "Transmission",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle transmission",
		client = {
			image = "veh_transmission.png",
		}
	},

	['bellend_muzzle_brake'] = {
		label = "Bellend Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "bellend_muzzle_brake.png",
		}
	},

	['clip_attachment'] = {
		label = "Clip",
		weight = 1000,
		stack = true,
		close = true,
		description = "A clip for a weapon",
		client = {
			image = "clip_attachment.png",
		}
	},

	['digicamo_attachment'] = {
		label = "Digital Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A digital camo for a weapon",
		client = {
			image = "digicamo_attachment.png",
		}
	},

	['veh_wheels'] = {
		label = "Wheels",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle wheels",
		client = {
			image = "veh_wheels.png",
		}
	},

	['aluminumoxide'] = {
		label = "Aluminium Powder",
		weight = 100,
		stack = true,
		close = false,
		description = "Some powder to mix with",
		client = {
			image = "aluminumoxide.png",
		}
	},

	['snikkel_candy'] = {
		label = "Snikkel",
		weight = 100,
		stack = true,
		close = true,
		description = "Some delicious candy :O",
		client = {
			image = "snikkel_candy.png",
		}
	},

	['squared_muzzle_brake'] = {
		label = "Squared Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "squared_muzzle_brake.png",
		}
	},

	['police_stormram'] = {
		label = "Stormram",
		weight = 18000,
		stack = true,
		close = true,
		description = "A nice tool to break into doors",
		client = {
			image = "police_stormram.png",
		}
	},

	['smallscope_attachment'] = {
		label = "Small Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A small scope for a weapon",
		client = {
			image = "smallscope_attachment.png",
		}
	},

	['heavyarmor'] = {
		label = "Heavy Armor",
		weight = 5000,
		stack = true,
		close = true,
		description = "Some protection won't hurt... right?",
		client = {
			image = "armor.png",
		}
	},

	['flashlight_attachment'] = {
		label = "Flashlight",
		weight = 1000,
		stack = true,
		close = true,
		description = "A flashlight for a weapon",
		client = {
			image = "flashlight_attachment.png",
		}
	},

	['luxuryfinish_attachment'] = {
		label = "Luxury Finish",
		weight = 1000,
		stack = true,
		close = true,
		description = "A luxury finish for a weapon",
		client = {
			image = "luxuryfinish_attachment.png",
		}
	},

	['casinochips'] = {
		label = "Casino Chips",
		weight = 0,
		stack = true,
		close = false,
		description = "Chips For Casino Gambling",
		client = {
			image = "casinochips.png",
		}
	},

	['veh_engine'] = {
		label = "Engine",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle engine",
		client = {
			image = "veh_engine.png",
		}
	},

	['medscope_attachment'] = {
		label = "Medium Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A medium scope for a weapon",
		client = {
			image = "medscope_attachment.png",
		}
	},

	['drum_attachment'] = {
		label = "Drum",
		weight = 1000,
		stack = true,
		close = true,
		description = "A drum for a weapon",
		client = {
			image = "drum_attachment.png",
		}
	},

	['fitbit'] = {
		label = "Fitbit",
		weight = 500,
		stack = false,
		close = true,
		description = "I like fitbit",
		client = {
			image = "fitbit.png",
		}
	},

	['printerdocument'] = {
		label = "Document",
		weight = 500,
		stack = false,
		close = true,
		description = "A nice document",
		client = {
			image = "printerdocument.png",
		}
	},

	['grip_attachment'] = {
		label = "Grip",
		weight = 1000,
		stack = true,
		close = true,
		description = "A grip for a weapon",
		client = {
			image = "grip_attachment.png",
		}
	},

	['twerks_candy'] = {
		label = "Twerks",
		weight = 100,
		stack = true,
		close = true,
		description = "Some delicious candy :O",
		client = {
			image = "twerks_candy.png",
		}
	},

	['markedbills'] = {
		label = "Marked Money",
		weight = 1000,
		stack = false,
		close = true,
		description = "Money?",
		client = {
			image = "markedbills.png",
		}
	},

	['tunerlaptop'] = {
		label = "Tunerchip",
		weight = 2000,
		stack = false,
		close = true,
		description = "With this tunerchip you can get your car on steroids... If you know what you're doing",
		client = {
			image = "tunerchip.png",
		}
	},

	['veh_interior'] = {
		label = "Interior",
		weight = 1000,
		stack = true,
		close = true,
		description = "Upgrade vehicle interior",
		client = {
			image = "veh_interior.png",
		}
	},

	['laptop'] = {
		label = "Laptop",
		weight = 4000,
		stack = true,
		close = true,
		description = "Expensive laptop",
		client = {
			image = "laptop.png",
		}
	},

	['tenkgoldchain'] = {
		label = "10k Gold Chain",
		weight = 2000,
		stack = true,
		close = true,
		description = "10 carat golden chain",
		client = {
			image = "10kgoldchain.png",
		}
	},

	['samsungphone'] = {
		label = "Samsung S10",
		weight = 1000,
		stack = true,
		close = true,
		description = "Very expensive phone",
		client = {
			image = "samsungphone.png",
		}
	},

	['precision_muzzle_brake'] = {
		label = "Precision Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "precision_muzzle_brake.png",
		}
	},

	['largescope_attachment'] = {
		label = "Large Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A large scope for a weapon",
		client = {
			image = "largescope_attachment.png",
		}
	},

	['weed_ogkush'] = {
		label = "OGKush 2g",
		weight = 200,
		stack = true,
		close = false,
		description = "A weed bag with 2g OG Kush",
		client = {
			image = "weed_baggy.png",
		}
	},

	['flat_muzzle_brake'] = {
		label = "Flat Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brake for a weapon",
		client = {
			image = "flat_muzzle_brake.png",
		}
	},

	['geocamo_attachment'] = {
		label = "Geometric Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A geometric camo for a weapon",
		client = {
			image = "geocamo_attachment.png",
		}
	},

	['tactical_muzzle_brake'] = {
		label = "Tactical Muzzle Brake",
		weight = 1000,
		stack = true,
		close = true,
		description = "A muzzle brakee for a weapon",
		client = {
			image = "tactical_muzzle_brake.png",
		}
	},

	['perseuscamo_attachment'] = {
		label = "Perseus Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A perseus camo for a weapon",
		client = {
			image = "perseuscamo_attachment.png",
		}
	},

	['skullcamo_attachment'] = {
		label = "Skull Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A skull camo for a weapon",
		client = {
			image = "skullcamo_attachment.png",
		}
	},

	['iphone'] = {
		label = "iPhone",
		weight = 1000,
		stack = true,
		close = true,
		description = "Very expensive phone",
		client = {
			image = "iphone.png",
		}
	},

	['walkstick'] = {
		label = "Walking Stick",
		weight = 1000,
		stack = true,
		close = true,
		description = "Walking stick for ya'll grannies out there.. HAHA",
		client = {
			image = "walkstick.png",
		}
	},

	['veh_turbo'] = {
		label = "Turbo",
		weight = 1000,
		stack = true,
		close = true,
		description = "Install vehicle turbo",
		client = {
			image = "veh_turbo.png",
		}
	},

	['certificate'] = {
		label = "Certificate",
		weight = 0,
		stack = true,
		close = true,
		description = "Certificate that proves you own certain stuff",
		client = {
			image = "certificate.png",
		}
	},

	['patriotcamo_attachment'] = {
		label = "Patriot Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A patriot camo for a weapon",
		client = {
			image = "patriotcamo_attachment.png",
		}
	},

	['brushcamo_attachment'] = {
		label = "Brushstroke Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A brushstroke camo for a weapon",
		client = {
			image = "brushcamo_attachment.png",
		}
	},

	['advscope_attachment'] = {
		label = "Advanced Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "An advanced scope for a weapon",
		client = {
			image = "advscope_attachment.png",
		}
	},

	['newscam'] = {
		label = "News Camera",
		weight = 100,
		stack = false,
		close = true,
		description = "A camera for the news",
		client = {
			image = "newscam.png",
		}
	},

	['thermalscope_attachment'] = {
		label = "Thermal Scope",
		weight = 1000,
		stack = true,
		close = true,
		description = "A thermal scope for a weapon",
		client = {
			image = "thermalscope_attachment.png",
		}
	},

	['veh_tint'] = {
		label = "Tints",
		weight = 1000,
		stack = true,
		close = true,
		description = "Install vehicle tint",
		client = {
			image = "veh_tint.png",
		}
	},

	['zebracamo_attachment'] = {
		label = "Zebra Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A zebra camo for a weapon",
		client = {
			image = "zebracamo_attachment.png",
		}
	},

	['boomcamo_attachment'] = {
		label = "Boom Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A boom camo for a weapon",
		client = {
			image = "boomcamo_attachment.png",
		}
	},

	['barrel_attachment'] = {
		label = "Barrel",
		weight = 1000,
		stack = true,
		close = true,
		description = "A barrel for a weapon",
		client = {
			image = "barrel_attachment.png",
		}
	},

	['labkey'] = {
		label = "Key",
		weight = 500,
		stack = false,
		close = true,
		description = "Key for a lock...?",
		client = {
			image = "labkey.png",
		}
	},

	['leopardcamo_attachment'] = {
		label = "Leopard Camo",
		weight = 1000,
		stack = true,
		close = true,
		description = "A leopard camo for a weapon",
		client = {
			image = "leopardcamo_attachment.png",
		}
	},

	['cleaningkit'] = {
		label = "Cleaning Kit",
		weight = 250,
		stack = true,
		close = true,
		description = "A microfiber cloth with some soap will let your car sparkle again!",
		client = {
			image = "cleaningkit.png",
		}
	},
}