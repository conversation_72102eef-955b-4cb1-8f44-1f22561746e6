<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="32" height="32">
  <defs>
    <linearGradient id="stashGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9d4edd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6a1b9a;stop-opacity:1" />
    </linearGradient>
  </defs>
  <!-- Main Box -->
  <rect x="8" y="20" width="48" height="32" rx="4" fill="url(#stashGrad)" stroke="#5a189a" stroke-width="2"/>
  <!-- Handle -->
  <rect x="24" y="12" width="16" height="8" rx="2" fill="url(#stashGrad)" stroke="#5a189a" stroke-width="2"/>
  <!-- Lock -->
  <rect x="28" y="28" width="8" height="6" rx="1" fill="#ffffff" opacity="0.8"/>
  <circle cx="32" cy="31" r="1.5" fill="#5a189a"/>
  <!-- Details -->
  <rect x="12" y="24" width="40" height="2" fill="#ffffff" opacity="0.3"/>
  <rect x="12" y="44" width="40" height="2" fill="#ffffff" opacity="0.3"/>
</svg>
