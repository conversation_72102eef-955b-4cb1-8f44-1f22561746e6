<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="32" height="32">
  <defs>
    <linearGradient id="shopGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9d4edd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6a1b9a;stop-opacity:1" />
    </linearGradient>
  </defs>
  <!-- Cart Body -->
  <rect x="12" y="24" width="32" height="20" rx="2" fill="url(#shopGrad)" stroke="#5a189a" stroke-width="2"/>
  <!-- Car<PERSON>le -->
  <rect x="8" y="20" width="4" height="28" rx="2" fill="url(#shopGrad)" stroke="#5a189a" stroke-width="2"/>
  <!-- Cart Grid -->
  <line x1="16" y1="28" x2="40" y2="28" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  <line x1="16" y1="32" x2="40" y2="32" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  <line x1="16" y1="36" x2="40" y2="36" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  <line x1="20" y1="24" x2="20" y2="44" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  <line x1="28" y1="24" x2="28" y2="44" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  <line x1="36" y1="24" x2="36" y2="44" stroke="#ffffff" stroke-width="1" opacity="0.4"/>
  <!-- Wheels -->
  <circle cx="18" cy="50" r="4" fill="url(#shopGrad)" stroke="#5a189a" stroke-width="2"/>
  <circle cx="38" cy="50" r="4" fill="url(#shopGrad)" stroke="#5a189a" stroke-width="2"/>
  <!-- Wheel Details -->
  <circle cx="18" cy="50" r="2" fill="#ffffff" opacity="0.6"/>
  <circle cx="38" cy="50" r="2" fill="#ffffff" opacity="0.6"/>
</svg>
