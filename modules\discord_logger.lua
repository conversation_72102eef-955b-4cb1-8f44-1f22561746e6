-- Discord Logger for ox_inventory
-- Custom implementation for Discord webhook logging

local DiscordLogger = {}

local webhook = GetConvar('inventory:discord_webhook', '')
local enabled = webhook ~= ''

if not enabled then
    return DiscordLogger
end

local headers = {
    ['Content-Type'] = 'application/json'
}

-- Color codes for different log types
local colors = {
    addItem = 65280,      -- Green
    removeItem = 16711680, -- Red
    swapSlots = 16776960,  -- Yellow
    admin = 16753920,      -- Orange
    buyItem = 3447003,     -- Blue
    default = 8421504      -- Gray
}

-- Format player name for logging
local function formatPlayerName(source)
    if type(source) == 'number' then
        local playerName = GetPlayerName(source)
        return playerName and ('%s (%s)'):format(playerName, source) or ('Player %s'):format(source)
    end
    return tostring(source)
end

-- Send log to Discord
function DiscordLogger.log(source, event, message, extraInfo)
    if not enabled then return end
    
    local playerInfo = formatPlayerName(source)
    local color = colors[event] or colors.default
    local timestamp = os.date('%Y-%m-%d %H:%M:%S')
    
    local embed = {
        title = ('ox_inventory - %s'):format(event),
        description = message,
        color = color,
        timestamp = os.date('!%Y-%m-%dT%H:%M:%SZ'),
        footer = {
            text = ('Server: %s | Time: %s'):format(GetConvar('sv_hostname', 'FiveM Server'), timestamp)
        },
        fields = {
            {
                name = 'Player',
                value = playerInfo,
                inline = true
            }
        }
    }
    
    if extraInfo then
        embed.fields[#embed.fields + 1] = {
            name = 'Additional Info',
            value = extraInfo,
            inline = true
        }
    end
    
    local payload = {
        username = 'ox_inventory Logger',
        avatar_url = 'https://i.imgur.com/4M34hi2.png',
        embeds = { embed }
    }
    
    PerformHttpRequest(webhook, function(statusCode, response)
        if statusCode ~= 200 and statusCode ~= 204 then
            print(('[ox_inventory] Discord webhook failed with status %s: %s'):format(statusCode, response))
        end
    end, 'POST', json.encode(payload), headers)
end

-- Override lib.logger for ox_inventory
if enabled then
    local originalLogger = lib.logger
    
    function lib.logger(source, event, message, extraInfo)
        -- Send to Discord
        DiscordLogger.log(source, event, message, extraInfo)
        
        -- Also send to original logger if configured
        if originalLogger and GetConvar('ox:logger', '') ~= '' then
            originalLogger(source, event, message, extraInfo)
        end
    end
end

return DiscordLogger
