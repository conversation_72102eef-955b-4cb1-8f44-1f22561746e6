-- اختبار بسيط للتأكد من إصلاح مشكلة inventory.items
-- هذا الملف للاختبار فقط ويمكن حذفه بعد التأكد من عمل الإصلاح

print("Testing inventory fix...")

-- محاكاة حالة inventory بدون items
local testInventory = {
    id = "test_inventory",
    type = "stash",
    -- items = nil -- هذا سيسبب الخطأ الأصلي
}

-- اختبار التحقق من وجود items
local function testInventoryItemsCheck(inventory)
    if not inventory.items then
        print("✓ تم اكتشاف inventory بدون items بنجاح")
        return false, 'inventory_not_available'
    end
    
    print("✓ inventory يحتوي على items")
    return true
end

-- تشغيل الاختبار
local success, error = testInventoryItemsCheck(testInventory)
if not success then
    print("✓ الإصلاح يعمل بشكل صحيح - تم منع الخطأ")
    print("رسالة الخطأ:", error)
else
    print("✗ هناك مشكلة في الإصلاح")
end

-- اختبار مع inventory صحيح
local validInventory = {
    id = "valid_inventory",
    type = "stash",
    items = {}
}

local success2, error2 = testInventoryItemsCheck(validInventory)
if success2 then
    print("✓ inventory صحيح يعمل بشكل طبيعي")
else
    print("✗ مشكلة مع inventory صحيح")
end

print("انتهى الاختبار")
