return {
-- Police Trash
-- {
-- 	coords = vec3(482.51, -995.62, 30.69),
-- 	target = {
-- 		loc = vec3(482.51, -995.62, 30.69),
-- 		length = 0.5,
-- 		width = 2.0,
-- 		heading = 90.0,
-- 		minZ = 30.49,
-- 		maxZ = 31.09,
-- 		label = 'Open Trash'
-- 	},
-- 	name = 'police_trash',
-- 	label = 'Police Trash',
-- 	owner = false,
-- 	slots = 300,
-- 	weight = 4000000
-- },

-- -- Evidence Locker
-- {
-- 	coords = vec3(482.51, -993.62, 30.69),
-- 	target = {
-- 		loc = vec3(482.51, -993.62, 30.69),
-- 		length = 0.5,
-- 		width = 2.0,
-- 		heading = 90.0,
-- 		minZ = 30.49,
-- 		maxZ = 31.09,
-- 		label = 'Open Evidence Locker'
-- 	},
-- 	name = 'evidence_1',
-- 	label = 'Evidence Locker 1',
-- 	owner = false,
-- 	slots = 300,
-- 	weight = 4000000
-- },

-- -- Evidence Drawer
-- {
-- 	coords = vec3(482.51, -991.62, 30.69),
-- 	target = {
-- 		loc = vec3(482.51, -991.62, 30.69),
-- 		length = 0.5,
-- 		width = 2.0,
-- 		heading = 90.0,
-- 		minZ = 30.49,
-- 		maxZ = 31.09,
-- 		label = 'Open Evidence Drawer'
-- 	},
-- 	name = 'evidence_1_1',
-- 	label = 'Evidence Locker 1 - Drawer 1',
-- 	owner = false,
-- 	slots = 500,
-- 	weight = 4000000
-- },

-- Police Personal Locker
{
	coords = vector3(81.74, -365.03, 41.19),
	target = {
		loc = vector3(81.74, -365.03, 41.19),
		length = 1.2,
		width = 5.6,
		heading = 0,
		minZ = 29.49,
		maxZ = 32.09,
		label = 'Open personal locker'
	},
	name = 'police_%s',
	label = 'Police Personal Locker',
	owner = true,
	slots = 50,
	weight = 100000,
	groups = { ['police'] = 0, ['bcso'] = 0, ['sasp'] = 0 }
},
	-- {
	-- 	coords = vec3(452.3, -991.4, 30.7),
	-- 	target = {
	-- 		loc = vec3(451.25, -994.28, 30.69),
	-- 		length = 1.2,
	-- 		width = 5.6,
	-- 		heading = 0,
	-- 		minZ = 29.49,
	-- 		maxZ = 32.09,
	-- 		label = 'Open personal locker'
	-- 	},
	-- 	name = 'policelocker',
	-- 	label = 'Personal locker',
	-- 	owner = true,
	-- 	slots = 70,
	-- 	weight = 70000,
	-- 	groups = shared.police
	-- },

	-- {
	-- 	coords = vec3(301.3, -600.23, 43.28),
	-- 	target = {
	-- 		loc = vec3(301.82, -600.99, 43.29),
	-- 		length = 0.6,
	-- 		width = 1.8,
	-- 		heading = 340,
	-- 		minZ = 43.34,
	-- 		maxZ = 44.74,
	-- 		label = 'Open personal locker'
	-- 	},
	-- 	name = 'emslocker',
	-- 	label = 'Personal Locker',
	-- 	owner = true,
	-- 	slots = 70,
	-- 	weight = 70000,
	-- 	groups = {['ambulance'] = 0}
	-- },
}
