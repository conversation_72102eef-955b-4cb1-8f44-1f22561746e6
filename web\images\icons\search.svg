<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="32" height="32">
  <defs>
    <linearGradient id="searchGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9d4edd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6a1b9a;stop-opacity:1" />
    </linearGradient>
  </defs>
  <!-- Magnifying Glass Circle -->
  <circle cx="28" cy="28" r="16" fill="none" stroke="url(#searchGrad)" stroke-width="4"/>
  <!-- Glass Interior -->
  <circle cx="28" cy="28" r="12" fill="#ffffff" opacity="0.1"/>
  <!-- Handle -->
  <line x1="40" y1="40" x2="52" y2="52" stroke="url(#searchGrad)" stroke-width="4" stroke-linecap="round"/>
  <!-- Reflection -->
  <path d="M20 22 Q24 18 28 22" stroke="#ffffff" stroke-width="2" fill="none" opacity="0.6"/>
  <!-- Search Details -->
  <circle cx="28" cy="28" r="8" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
  <circle cx="28" cy="28" r="2" fill="#ffffff" opacity="0.5"/>
</svg>
