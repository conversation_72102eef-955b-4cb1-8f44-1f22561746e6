
local Inventory = require 'modules.inventory.server'

RegisterNetEvent('ox_inventory:openInventory')
AddEventHandler('ox_inventory:openInventory', function(invType, data)
    local source = source

    if invType == 'stash' and type(data) == 'string' and string.find(data, "trash_") == 1 then
        local trashId = data
        local trashName = 'Disposal Safe'

        local trashStash = Inventory(trashId)

        if not trashStash then
            Inventory.Create(trashId, trashName, 'stash', 30, 0, 100000, false)
        end

        TriggerClientEvent('ox_inventory:openInventory', source, 'stash', trashId)
    end
end)

AddEventHandler('ox_inventory:closeInventory', function(playerId, inventoryId)
    if type(inventoryId) == 'string' and string.find(inventoryId, "trash_") == 1 then
        Inventory.Remove(inventoryId)
    end
end)
