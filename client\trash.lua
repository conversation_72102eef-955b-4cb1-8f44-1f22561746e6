-- Trash Containers Integration with interact system
local TrashModel = {
    "prop_bin_05a",
    "prop_bin_01a",
    "v_ind_cfwaste",
    "prop_bin_08a",
    "prop_bin_08open",
    "prop_bin_02a",
    "prop_bin_03a",
    "prop_bin_04a",
    "prop_bin_06a",
    "prop_bin_07a",
    "prop_bin_07b",
    "prop_bin_07c",
    "prop_bin_07d",
    "prop_bin_10a",
    "prop_bin_10b",
    "prop_bin_11a",
    "prop_bin_11b",
    "prop_dumpster_3a",
    "prop_dumpster_02a",
    "prop_dumpster_02b",
    "prop_bin_12a"
}

RegisterNetEvent('inventory:client:OpenTrash', function()
    if cache.vehicle then
        return lib.notify({ type = 'error', description = 'You cannot use disposal safes from a vehicle' })
    end

    local trashId = "trash_"..math.random(1000, 9999).."_"..math.random(1000, 9999).."_"..math.random(1000, 9999)

    TriggerServerEvent('ox_inventory:openInventory', 'stash', trashId)
end)

CreateThread(function()
    Wait(1000)

    for k, v in pairs(TrashModel) do
        Wait(100)
        exports.interact:AddModelInteraction({
            model = v,
            offset = vec3(0.0, 0.0, 0.5),
            id = 'Trash' .. k,
            distance = 3.5,
            interactDst = 1.5,
            ignoreLos = false,
            options = {
                {
                    label = 'Use Disposal Safe',
                    event = "inventory:client:OpenTrash",
                }
            }
        })
    end
end)
